import { Server as SocketIOServer } from 'socket.io';
import { AIOrchestrator } from '../services/AIOrchestrator';
import { SystemMonitor } from '../services/SystemMonitor';
import { TerminalManager } from '../services/TerminalManager';
import { FileSystemService } from '../services/FileSystemService';
import { PredictiveIntelligence } from '../services/PredictiveIntelligence';

interface Services {
  aiOrchestrator: AIOrchestrator;
  systemMonitor: SystemMonitor;
  terminalManager: TerminalManager;
  fileSystemService: FileSystemService;
  predictiveIntelligence: PredictiveIntelligence;
}

export function setupSocketHandlers(io: SocketIOServer, services: Services) {
  const { aiOrchestrator, systemMonitor, terminalManager, fileSystemService, predictiveIntelligence } = services;

  io.on('connection', (socket) => {
    console.log(`🔌 Client connected: ${socket.id}`);

    // Send initial data
    socket.emit('system:metrics', systemMonitor.getCurrentMetrics());
    socket.emit('terminal:sessions', terminalManager.getAllSessions());
    socket.emit('ai:providers', aiOrchestrator.getAvailableProviders());

    // AI Handlers
    socket.on('ai:query', async (data) => {
      try {
        const { query, context, provider, priority = 'medium' } = data;
        
        const request = {
          id: Math.random().toString(36).substr(2, 9),
          query,
          context: context || {
            currentFile: undefined,
            openFiles: [],
            projectType: 'unknown',
            recentCommands: [],
            systemState: systemMonitor.getCurrentMetrics() || {
              cpu: { usage: 0, temperature: 0, cores: 0 },
              memory: { total: 0, used: 0, free: 0, percentage: 0 },
              disk: { total: 0, used: 0, free: 0, percentage: 0 },
              network: { upload: 0, download: 0, latency: 0 },
              timestamp: Date.now(),
            },
          },
          provider,
          priority,
          timestamp: Date.now(),
        };

        const response = await aiOrchestrator.routeQuery(request);
        socket.emit('ai:response', response);
      } catch (error) {
        socket.emit('ai:error', { error: (error as Error).message });
      }
    });

    // Terminal Handlers
    socket.on('terminal:create', (data) => {
      try {
        const { name, workingDirectory } = data;
        const session = terminalManager.createSession(name, workingDirectory);
        socket.emit('terminal:session-created', session);
        io.emit('terminal:sessions', terminalManager.getAllSessions());
      } catch (error) {
        socket.emit('terminal:error', { error: (error as Error).message });
      }
    });

    socket.on('terminal:execute', (data) => {
      try {
        const { sessionId, command } = data;
        const block = terminalManager.executeCommand(sessionId, command);
        socket.emit('terminal:command-executed', { sessionId, block });
      } catch (error) {
        socket.emit('terminal:error', { error: (error as Error).message });
      }
    });

    socket.on('terminal:input', (data) => {
      try {
        const { sessionId, input } = data;
        terminalManager.writeToTerminal(sessionId, input);
      } catch (error) {
        socket.emit('terminal:error', { error: (error as Error).message });
      }
    });

    socket.on('terminal:resize', (data) => {
      try {
        const { sessionId, cols, rows } = data;
        terminalManager.resizeTerminal(sessionId, cols, rows);
      } catch (error) {
        socket.emit('terminal:error', { error: (error as Error).message });
      }
    });

    socket.on('terminal:set-active', (data) => {
      try {
        const { sessionId } = data;
        terminalManager.setActiveSession(sessionId);
        io.emit('terminal:active-changed', sessionId);
      } catch (error) {
        socket.emit('terminal:error', { error: (error as Error).message });
      }
    });

    socket.on('terminal:close', (data) => {
      try {
        const { sessionId } = data;
        terminalManager.closeSession(sessionId);
        io.emit('terminal:sessions', terminalManager.getAllSessions());
      } catch (error) {
        socket.emit('terminal:error', { error: (error as Error).message });
      }
    });

    // File System Handlers
    socket.on('filesystem:watch', (data) => {
      try {
        const { path } = data;
        fileSystemService.watchDirectory(path);
        socket.emit('filesystem:watching', { path });
      } catch (error) {
        socket.emit('filesystem:error', { error: (error as Error).message });
      }
    });

    socket.on('filesystem:unwatch', (data) => {
      try {
        const { path } = data;
        fileSystemService.stopWatching(path);
        socket.emit('filesystem:unwatched', { path });
      } catch (error) {
        socket.emit('filesystem:error', { error: (error as Error).message });
      }
    });

    socket.on('filesystem:read', async (data) => {
      try {
        const { path } = data;
        const content = await fileSystemService.readFile(path);
        socket.emit('filesystem:file-content', { path, content });
      } catch (error) {
        socket.emit('filesystem:error', { error: (error as Error).message });
      }
    });

    socket.on('filesystem:write', async (data) => {
      try {
        const { path, content } = data;
        await fileSystemService.writeFile(path, content);
        socket.emit('filesystem:file-written', { path });
      } catch (error) {
        socket.emit('filesystem:error', { error: (error as Error).message });
      }
    });

    // System Handlers
    socket.on('system:get-processes', async () => {
      try {
        const processes = await systemMonitor.getProcessList();
        socket.emit('system:processes', processes);
      } catch (error) {
        socket.emit('system:error', { error: (error as Error).message });
      }
    });

    socket.on('system:get-info', async () => {
      try {
        const info = await systemMonitor.getSystemInfo();
        socket.emit('system:info', info);
      } catch (error) {
        socket.emit('system:error', { error: (error as Error).message });
      }
    });

    // Predictive Intelligence Handlers
    socket.on('intelligence:analyze', async (data) => {
      try {
        const { path } = data;
        const insights = await predictiveIntelligence.analyzeCodebase(path || process.cwd());
        socket.emit('intelligence:insights', insights);
      } catch (error) {
        socket.emit('intelligence:error', { error: (error as Error).message });
      }
    });

    socket.on('intelligence:suggestions', async (data) => {
      try {
        const { currentFile, cursorPosition, recentCode } = data;
        const suggestions = await predictiveIntelligence.getContextualSuggestions(
          currentFile,
          cursorPosition,
          recentCode
        );
        socket.emit('intelligence:suggestions', suggestions);
      } catch (error) {
        socket.emit('intelligence:error', { error: (error as Error).message });
      }
    });

    socket.on('intelligence:action', (data) => {
      try {
        const { action, context } = data;
        predictiveIntelligence.recordUserAction(action, context);
        socket.emit('intelligence:action-recorded', { success: true });
      } catch (error) {
        socket.emit('intelligence:error', { error: (error as Error).message });
      }
    });

    // Disconnect handler
    socket.on('disconnect', () => {
      console.log(`🔌 Client disconnected: ${socket.id}`);
    });
  });

  // Set up service event forwarding
  setupServiceEventForwarding(io, services);
}

function setupServiceEventForwarding(io: SocketIOServer, services: Services) {
  const { systemMonitor, terminalManager, fileSystemService } = services;

  // Forward system monitor events
  systemMonitor.on('metrics', (metrics) => {
    io.emit('system:metrics', metrics);
  });

  systemMonitor.on('alerts', (alerts) => {
    io.emit('system:alerts', alerts);
  });

  // Forward terminal manager events
  terminalManager.on('sessionCreated', (session) => {
    io.emit('terminal:session-created', session);
  });

  terminalManager.on('sessionClosed', (sessionId) => {
    io.emit('terminal:session-closed', sessionId);
  });

  terminalManager.on('terminalOutput', (data) => {
    io.emit('terminal:output', data);
  });

  terminalManager.on('commandExecuted', (data) => {
    io.emit('terminal:command-executed', data);
  });

  terminalManager.on('activeSessionChanged', (sessionId) => {
    io.emit('terminal:active-changed', sessionId);
  });

  // Forward file system events
  fileSystemService.on('fileChanged', (data) => {
    io.emit('filesystem:changed', data);
  });
}
