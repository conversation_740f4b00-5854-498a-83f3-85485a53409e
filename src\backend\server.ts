import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';

import { AIOrchestrator } from './services/AIOrchestrator';
import { SystemMonitor } from './services/SystemMonitor';
import { TerminalManager } from './services/TerminalManager';
import { FileSystemService } from './services/FileSystemService';
// import { PredictiveIntelligence } from './services/PredictiveIntelligence';
import { setupRoutes } from './routes/index';
import { setupSocketHandlers } from './socket/index';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000'],
    methods: ['GET', 'POST'],
  },
});

const PORT = process.env.PORT || 8000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../frontend')));

// Initialize core services
const aiOrchestrator = new AIOrchestrator();
const systemMonitor = new SystemMonitor();
const terminalManager = new TerminalManager();
const fileSystemService = new FileSystemService();
// const predictiveIntelligence = new PredictiveIntelligence(
//   aiOrchestrator,
//   fileSystemService,
//   systemMonitor
// );

// Setup routes
setupRoutes(app, {
  aiOrchestrator,
  systemMonitor,
  terminalManager,
  fileSystemService,
  predictiveIntelligence,
});

// Setup socket handlers
setupSocketHandlers(io, {
  aiOrchestrator,
  systemMonitor,
  terminalManager,
  fileSystemService,
  predictiveIntelligence,
});

// Start system monitoring
systemMonitor.start();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  systemMonitor.stop();
  terminalManager.cleanup();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  systemMonitor.stop();
  terminalManager.cleanup();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

server.listen(PORT, () => {
  console.log(`🚀 NEXUS AI Server running on port ${PORT}`);
  console.log(`🌐 Frontend: http://localhost:3000`);
  console.log(`🔧 API: http://localhost:${PORT}/api`);
  console.log(`🤖 AI Providers initialized: ${aiOrchestrator.getAvailableProviders().length}`);
});
