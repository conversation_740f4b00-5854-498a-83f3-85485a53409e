import React, { useState, useEffect } from 'react';
import { useFileSystem } from '../contexts/FileSystemContext';
import { FileSystemNode } from '../../../shared/types';

interface FileExplorerProps {
  className?: string;
}

interface FileNodeProps {
  node: FileSystemNode;
  level: number;
  onFileSelect: (path: string) => void;
  selectedFile: string | null;
  expandedDirs: Set<string>;
  onToggleDir: (path: string) => void;
}

function FileNode({ node, level, onFileSelect, selectedFile, expandedDirs, onToggleDir }: FileNodeProps) {
  const isExpanded = expandedDirs.has(node.path);
  const isSelected = selectedFile === node.path;

  const getFileIcon = (name: string, isDirectory: boolean) => {
    if (isDirectory) {
      return isExpanded ? '📂' : '📁';
    }
    
    const ext = name.split('.').pop()?.toLowerCase();
    const iconMap: Record<string, string> = {
      'ts': '🔷',
      'tsx': '⚛️',
      'js': '🟨',
      'jsx': '⚛️',
      'py': '🐍',
      'java': '☕',
      'cpp': '⚙️',
      'c': '⚙️',
      'cs': '🔷',
      'php': '🐘',
      'rb': '💎',
      'go': '🐹',
      'rs': '🦀',
      'html': '🌐',
      'css': '🎨',
      'scss': '🎨',
      'json': '📋',
      'xml': '📄',
      'yaml': '📄',
      'yml': '📄',
      'md': '📝',
      'sql': '🗃️',
      'sh': '🐚',
      'bash': '🐚',
      'dockerfile': '🐳',
      'gitignore': '🚫',
      'env': '🔐',
    };
    
    return iconMap[ext || ''] || '📄';
  };

  const handleClick = () => {
    if (node.type === 'directory') {
      onToggleDir(node.path);
    } else {
      onFileSelect(node.path);
    }
  };

  return (
    <div>
      <div
        className={`flex items-center space-x-2 px-2 py-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-700 ${
          isSelected ? 'bg-blue-100 dark:bg-blue-900/30' : ''
        }`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
      >
        <span className="text-sm">{getFileIcon(node.name, node.type === 'directory')}</span>
        <span className={`text-sm truncate ${isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}>
          {node.name}
        </span>
        {node.type === 'file' && node.size && (
          <span className="text-xs text-gray-400 ml-auto">
            {formatFileSize(node.size)}
          </span>
        )}
      </div>
      
      {node.type === 'directory' && isExpanded && node.children && (
        <div>
          {node.children.map((child) => (
            <FileNode
              key={child.path}
              node={child}
              level={level + 1}
              onFileSelect={onFileSelect}
              selectedFile={selectedFile}
              expandedDirs={expandedDirs}
              onToggleDir={onToggleDir}
            />
          ))}
        </div>
      )}
    </div>
  );
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function FileExplorer({ className = '' }: FileExplorerProps) {
  const { fileTree, selectedFile, openFile, refreshFileTree } = useFileSystem();
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    // Load initial file tree
    refreshFileTree();
  }, []);

  const handleToggleDir = (path: string) => {
    setExpandedDirs(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  const handleFileSelect = (path: string) => {
    openFile(path);
  };

  const filteredTree = React.useMemo(() => {
    if (!fileTree || !searchQuery) return fileTree;
    
    // Simple search implementation - in a real app, this would be more sophisticated
    const filterNode = (node: FileSystemNode): FileSystemNode | null => {
      const matchesSearch = node.name.toLowerCase().includes(searchQuery.toLowerCase());
      
      if (node.type === 'file') {
        return matchesSearch ? node : null;
      }
      
      // For directories, include if name matches or any children match
      const filteredChildren = node.children?.map(filterNode).filter(Boolean) || [];
      
      if (matchesSearch || filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren as FileSystemNode[]
        };
      }
      
      return null;
    };
    
    return filterNode(fileTree);
  }, [fileTree, searchQuery]);

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-dark-700">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Explorer</h3>
          <button
            onClick={() => refreshFileTree()}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            title="Refresh"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <input
            type="text"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-3 py-1 text-sm bg-gray-100 dark:bg-dark-700 border border-gray-200 dark:border-dark-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <svg className="absolute right-2 top-1.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto">
        {filteredTree ? (
          <FileNode
            node={filteredTree}
            level={0}
            onFileSelect={handleFileSelect}
            selectedFile={selectedFile}
            expandedDirs={expandedDirs}
            onToggleDir={handleToggleDir}
          />
        ) : (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <svg className="w-12 h-12 mx-auto mb-2 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
            </svg>
            <p className="text-sm">No files found</p>
            <button
              onClick={() => refreshFileTree()}
              className="mt-2 text-xs text-blue-500 hover:text-blue-600"
            >
              Refresh Explorer
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

export default FileExplorer;
