"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalManager = void 0;
const events_1 = require("events");
class TerminalManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.sessions = new Map();
        this.terminals = new Map();
        this.activeSessionId = null;
    }
    createSession(name, workingDirectory) {
        const sessionId = this.generateId();
        const sessionName = name || `Terminal ${this.sessions.size + 1}`;
        const cwd = workingDirectory || process.cwd();
        const session = {
            id: sessionId,
            name: sessionName,
            workingDirectory: cwd,
            environment: Object.fromEntries(Object.entries(process.env).filter(([_, value]) => value !== undefined)),
            blocks: [],
            isActive: false,
            createdAt: Date.now(),
            lastActivity: Date.now(),
        };
        // Create a simple command execution environment
        // Note: This is a simplified version without full terminal emulation
        const mockProcess = {
            write: (data) => {
                // For now, we'll simulate command execution
                console.log(`Terminal ${sessionId} input: ${data}`);
            },
            kill: () => {
                console.log(`Terminal ${sessionId} killed`);
            },
            resize: (cols, rows) => {
                console.log(`Terminal ${sessionId} resized to ${cols}x${rows}`);
            }
        };
        this.sessions.set(sessionId, session);
        this.terminals.set(sessionId, mockProcess);
        // Set as active if it's the first session
        if (this.sessions.size === 1) {
            this.setActiveSession(sessionId);
        }
        console.log(`📟 Created terminal session: ${sessionName} (${sessionId})`);
        this.emit('sessionCreated', session);
        return session;
    }
    executeCommand(sessionId, command) {
        const session = this.sessions.get(sessionId);
        const terminal = this.terminals.get(sessionId);
        if (!session || !terminal) {
            throw new Error(`Terminal session ${sessionId} not found`);
        }
        const blockId = this.generateId();
        const block = {
            id: blockId,
            command: command.trim(),
            output: '',
            status: 'running',
            startTime: Date.now(),
            metadata: {
                workingDirectory: session.workingDirectory,
                environment: session.environment,
            },
        };
        // Add block to session
        session.blocks.push(block);
        session.lastActivity = Date.now();
        // Simulate command execution
        this.simulateCommandExecution(sessionId, blockId, command);
        console.log(`⚡ Executing command in ${sessionId}: ${command}`);
        this.emit('commandExecuted', { sessionId, block });
        return block;
    }
    simulateCommandExecution(sessionId, blockId, command) {
        // Simulate command execution with mock output
        setTimeout(() => {
            const session = this.sessions.get(sessionId);
            if (!session)
                return;
            const block = session.blocks.find(b => b.id === blockId);
            if (!block)
                return;
            // Generate mock output based on command
            let output = '';
            if (command.includes('ls') || command.includes('dir')) {
                output = 'package.json\nsrc/\nnode_modules/\nREADME.md\n';
            }
            else if (command.includes('pwd') || command.includes('cd')) {
                output = session.workingDirectory + '\n';
            }
            else if (command.includes('echo')) {
                output = command.replace('echo ', '') + '\n';
            }
            else {
                output = `Command executed: ${command}\n`;
            }
            block.output = output;
            block.status = 'completed';
            block.endTime = Date.now();
            block.exitCode = 0;
            this.emit('terminalOutput', { sessionId, data: output, blockId });
        }, 500); // Simulate 500ms execution time
    }
    handleTerminalOutput(sessionId, data) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return;
        // Find the most recent running block
        const runningBlock = session.blocks
            .slice()
            .reverse()
            .find(block => block.status === 'running');
        if (runningBlock) {
            runningBlock.output += data;
            // Check if command completed (basic heuristic)
            if (this.isCommandComplete(data)) {
                runningBlock.status = 'completed';
                runningBlock.endTime = Date.now();
                runningBlock.exitCode = 0; // Assume success for now
            }
        }
        session.lastActivity = Date.now();
        this.emit('terminalOutput', { sessionId, data, blockId: runningBlock?.id });
    }
    handleTerminalExit(sessionId, exitCode) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return;
        // Mark any running blocks as failed
        session.blocks
            .filter(block => block.status === 'running')
            .forEach(block => {
            block.status = 'failed';
            block.endTime = Date.now();
            block.exitCode = exitCode;
        });
        console.log(`📟 Terminal session ${sessionId} exited with code ${exitCode}`);
        this.emit('sessionExited', { sessionId, exitCode });
    }
    isCommandComplete(output) {
        // Simple heuristics to detect command completion
        // This is basic and would need improvement for production
        const prompts = ['$', '>', '#', 'PS>', 'C:\\'];
        return prompts.some(prompt => output.includes(prompt));
    }
    writeToTerminal(sessionId, data) {
        const terminal = this.terminals.get(sessionId);
        if (terminal) {
            // For our mock terminal, we'll just log the input
            console.log(`Terminal ${sessionId} input: ${data}`);
        }
    }
    resizeTerminal(sessionId, cols, rows) {
        const terminal = this.terminals.get(sessionId);
        if (terminal) {
            // For our mock terminal, we'll just log the resize
            console.log(`Terminal ${sessionId} resized to ${cols}x${rows}`);
        }
    }
    setActiveSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new Error(`Session ${sessionId} not found`);
        }
        // Deactivate current active session
        if (this.activeSessionId) {
            const currentActive = this.sessions.get(this.activeSessionId);
            if (currentActive) {
                currentActive.isActive = false;
            }
        }
        // Activate new session
        session.isActive = true;
        this.activeSessionId = sessionId;
        session.lastActivity = Date.now();
        this.emit('activeSessionChanged', sessionId);
    }
    getSession(sessionId) {
        return this.sessions.get(sessionId);
    }
    getAllSessions() {
        return Array.from(this.sessions.values());
    }
    getActiveSession() {
        return this.activeSessionId ? this.sessions.get(this.activeSessionId) || null : null;
    }
    closeSession(sessionId) {
        const terminal = this.terminals.get(sessionId);
        const session = this.sessions.get(sessionId);
        if (terminal) {
            terminal.kill();
            this.terminals.delete(sessionId);
        }
        if (session) {
            this.sessions.delete(sessionId);
            // If this was the active session, activate another one
            if (this.activeSessionId === sessionId) {
                this.activeSessionId = null;
                const remainingSessions = Array.from(this.sessions.values());
                if (remainingSessions.length > 0) {
                    this.setActiveSession(remainingSessions[0].id);
                }
            }
        }
        console.log(`📟 Closed terminal session: ${sessionId}`);
        this.emit('sessionClosed', sessionId);
    }
    getCommandHistory(sessionId, limit) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return [];
        const blocks = session.blocks.slice();
        return limit ? blocks.slice(-limit) : blocks;
    }
    searchCommandHistory(sessionId, query) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return [];
        return session.blocks.filter(block => block.command.toLowerCase().includes(query.toLowerCase()) ||
            block.output.toLowerCase().includes(query.toLowerCase()));
    }
    getSessionStats(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session)
            return null;
        const totalCommands = session.blocks.length;
        const successfulCommands = session.blocks.filter(b => b.status === 'completed').length;
        const failedCommands = session.blocks.filter(b => b.status === 'failed').length;
        const runningCommands = session.blocks.filter(b => b.status === 'running').length;
        const avgExecutionTime = session.blocks
            .filter(b => b.endTime)
            .reduce((sum, b) => sum + (b.endTime - b.startTime), 0) /
            (session.blocks.filter(b => b.endTime).length || 1);
        return {
            totalCommands,
            successfulCommands,
            failedCommands,
            runningCommands,
            successRate: totalCommands > 0 ? (successfulCommands / totalCommands) * 100 : 0,
            avgExecutionTime: Math.round(avgExecutionTime),
            uptime: Date.now() - session.createdAt,
            lastActivity: session.lastActivity,
        };
    }
    cleanup() {
        console.log('🧹 Cleaning up terminal sessions...');
        // Close all terminals
        for (const [sessionId, terminal] of this.terminals) {
            try {
                terminal.kill();
            }
            catch (error) {
                console.error(`Error closing terminal ${sessionId}:`, error);
            }
        }
        this.terminals.clear();
        this.sessions.clear();
        this.activeSessionId = null;
    }
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
}
exports.TerminalManager = TerminalManager;
