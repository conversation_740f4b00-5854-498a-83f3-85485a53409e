import React from 'react';
import TerminalView from './TerminalView';
import AI<PERSON>hat from './AIChat';
import FileEditor from './FileEditor';
import SystemDashboard from './SystemDashboard';
import CodeAnalysis from './CodeAnalysis';
import WorkflowAutomation from './WorkflowAutomation';
import PerformanceMonitor from './PerformanceMonitor';

interface MainContentProps {
  activePanel: 'terminal' | 'ai' | 'files' | 'system' | 'analysis' | 'workflow' | 'performance';
  sidebarCollapsed: boolean;
}

function MainContent({ activePanel, sidebarCollapsed }: MainContentProps) {
  const renderContent = () => {
    switch (activePanel) {
      case 'terminal':
        return <TerminalView />;
      case 'ai':
        return <AIChat />;
      case 'files':
        return <FileEditor />;
      case 'system':
        return <SystemDashboard />;
      case 'analysis':
        return <CodeAnalysis />;
      case 'workflow':
        return <WorkflowAutomation />;
      case 'performance':
        return <PerformanceMonitor />;
      default:
        return <TerminalView />;
    }
  };

  return (
    <div className="flex-1 flex flex-col bg-gray-50 dark:bg-dark-900 overflow-hidden">
      {renderContent()}
    </div>
  );
}

export default MainContent;
