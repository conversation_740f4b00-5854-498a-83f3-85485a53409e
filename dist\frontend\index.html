<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/nexus-icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>NEXUS AI - Unified Development Environment</title>
    <meta name="description" content="NEXUS AI - Revolutionary development environment with JARVIS-like intelligence" />
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Theme detection script -->
    <script>
      // Detect system theme preference
      if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    </script>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        color: white;
        font-family: 'JetBrains Mono', monospace;
      }
      
      .loading-logo {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 2rem;
        background: linear-gradient(45deg, #0ea5e9, #3b82f6, #8b5cf6);
        background-size: 200% 200%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradient 3s ease infinite;
      }
      
      .loading-text {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.8;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(14, 165, 233, 0.3);
        border-top: 3px solid #0ea5e9;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-progress {
        margin-top: 2rem;
        text-align: center;
      }
      
      .progress-bar {
        width: 300px;
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;
        margin: 1rem auto;
      }
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #0ea5e9, #3b82f6);
        border-radius: 2px;
        width: 0%;
        animation: progress 3s ease-in-out;
      }
      
      @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      @keyframes progress {
        0% { width: 0%; }
        100% { width: 100%; }
      }
      
      /* Hide loading screen when app is ready */
      .app-ready #loading-screen {
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.5s ease-out;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-bff6fdd0.js"></script>
    <link rel="stylesheet" href="/assets/index-3da54e0d.css">
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">NEXUS AI</div>
      <div class="loading-text">Initializing Intelligent Development Environment...</div>
      <div class="loading-spinner"></div>
      <div class="loading-progress">
        <div class="progress-bar">
          <div class="progress-fill"></div>
        </div>
        <div id="loading-status">Starting AI orchestrator...</div>
      </div>
    </div>
    
    <!-- Main App Container -->
    <div id="root"></div>
    
    <!-- App initialization script -->
    <script>
      // Simulate loading progress
      const loadingSteps = [
        'Starting AI orchestrator...',
        'Connecting to providers...',
        'Initializing system monitor...',
        'Setting up terminal manager...',
        'Loading file system...',
        'Establishing WebSocket connection...',
        'Ready to assist!'
      ];
      
      let currentStep = 0;
      const statusElement = document.getElementById('loading-status');
      
      const updateLoadingStatus = () => {
        if (currentStep < loadingSteps.length) {
          statusElement.textContent = loadingSteps[currentStep];
          currentStep++;
          setTimeout(updateLoadingStatus, 500);
        }
      };
      
      updateLoadingStatus();
      
      // Hide loading screen when React app is ready
      window.addEventListener('nexus-ready', () => {
        document.body.classList.add('app-ready');
        setTimeout(() => {
          document.getElementById('loading-screen').style.display = 'none';
        }, 500);
      });
    </script>
    
    
  </body>
</html>
