import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useSocket } from './SocketContext';
import { FileSystemNode, ProjectContext } from '../../../shared/types';

interface FileSystemContextType {
  fileTree: FileSystemNode | null;
  currentProject: ProjectContext | null;
  selectedFile: string | null;
  openFiles: string[];
  watchedDirectories: string[];
  fileContents: Record<string, string>;
  readFile: (path: string) => Promise<string>;
  writeFile: (path: string, content: string) => void;
  createDirectory: (path: string) => void;
  deleteFile: (path: string) => void;
  openFile: (path: string) => void;
  closeFile: (path: string) => void;
  setSelectedFile: (path: string | null) => void;
  watchDirectory: (path: string) => void;
  unwatchDirectory: (path: string) => void;
  refreshFileTree: (rootPath?: string) => void;
  analyzeProject: (rootPath?: string) => void;
  searchFiles: (query: string, extensions?: string[]) => Promise<string[]>;
  updateFileContent: (path: string, content: string) => void;
}

const FileSystemContext = createContext<FileSystemContextType | undefined>(undefined);

interface FileSystemProviderProps {
  children: ReactNode;
}

export function FileSystemProvider({ children }: FileSystemProviderProps) {
  const { socket, connected, on, off, emit } = useSocket();
  const [fileTree, setFileTree] = useState<FileSystemNode | null>(null);
  const [currentProject, setCurrentProject] = useState<ProjectContext | null>(null);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [openFiles, setOpenFiles] = useState<string[]>([]);
  const [watchedDirectories, setWatchedDirectories] = useState<string[]>([]);
  const [fileContents, setFileContents] = useState<Record<string, string>>({});

  useEffect(() => {
    if (!connected) return;

    // Set up event listeners
    const handleFileChanged = (data: { path: string; type: 'added' | 'modified' | 'deleted' }) => {
      console.log(`File ${data.type}: ${data.path}`);
      
      // Refresh file tree if needed
      if (data.type === 'added' || data.type === 'deleted') {
        refreshFileTree();
      }
      
      // If an open file was modified externally, we might want to reload it
      if (data.type === 'modified' && openFiles.includes(data.path)) {
        // Could show a notification or auto-reload
        console.log(`Open file modified externally: ${data.path}`);
      }
    };

    const handleFileContent = (data: { path: string; content: string }) => {
      setFileContents(prev => new Map(prev.set(data.path, data.content)));
    };

    const handleFileWritten = (data: { path: string }) => {
      console.log(`File written: ${data.path}`);
    };

    const handleWatching = (data: { path: string }) => {
      setWatchedDirectories(prev => [...prev.filter(p => p !== data.path), data.path]);
    };

    const handleUnwatched = (data: { path: string }) => {
      setWatchedDirectories(prev => prev.filter(p => p !== data.path));
    };

    const handleError = (error: { error: string }) => {
      console.error('FileSystem error:', error.error);
    };

    on('filesystem:changed', handleFileChanged);
    on('filesystem:file-content', handleFileContent);
    on('filesystem:file-written', handleFileWritten);
    on('filesystem:watching', handleWatching);
    on('filesystem:unwatched', handleUnwatched);
    on('filesystem:error', handleError);

    // Initial data load
    refreshFileTree();
    analyzeProject();

    return () => {
      off('filesystem:changed', handleFileChanged);
      off('filesystem:file-content', handleFileContent);
      off('filesystem:file-written', handleFileWritten);
      off('filesystem:watching', handleWatching);
      off('filesystem:unwatched', handleUnwatched);
      off('filesystem:error', handleError);
    };
  }, [connected, on, off, emit]);

  const readFile = async (path: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      if (!connected) {
        reject(new Error('Not connected to server'));
        return;
      }

      // Check if we already have the content cached
      const cached = fileContents[path];
      if (cached !== undefined) {
        resolve(cached);
        return;
      }

      // Set up one-time listener for the response
      const handleContent = (data: { path: string; content: string }) => {
        if (data.path === path) {
          off('filesystem:file-content', handleContent);
          resolve(data.content);
        }
      };

      const handleError = (error: { error: string }) => {
        off('filesystem:file-content', handleContent);
        off('filesystem:error', handleError);
        reject(new Error(error.error));
      };

      on('filesystem:file-content', handleContent);
      on('filesystem:error', handleError);

      // Request the file
      emit('filesystem:read', { path });

      // Timeout after 10 seconds
      setTimeout(() => {
        off('filesystem:file-content', handleContent);
        off('filesystem:error', handleError);
        reject(new Error('File read timeout'));
      }, 10000);
    });
  };

  const writeFile = (path: string, content: string) => {
    if (connected) {
      emit('filesystem:write', { path, content });
      
      // Update local cache
      setFileContents(prev => ({ ...prev, [path]: content }));
    }
  };

  const createDirectory = (path: string) => {
    if (connected) {
      // This would need to be implemented in the backend
      console.log('Create directory:', path);
    }
  };

  const deleteFile = (path: string) => {
    if (connected) {
      // This would need to be implemented in the backend
      console.log('Delete file:', path);
    }
  };

  const openFile = async (path: string) => {
    if (!openFiles.includes(path)) {
      setOpenFiles(prev => [...prev, path]);
    }
    setSelectedFile(path);

    // Load file content if not already cached
    if (!fileContents[path]) {
      try {
        const content = await readFile(path);
        setFileContents(prev => ({ ...prev, [path]: content }));
      } catch (error) {
        console.error('Failed to load file content:', error);
        // Set empty content as fallback
        setFileContents(prev => ({ ...prev, [path]: '' }));
      }
    }
  };

  const updateFileContent = (path: string, content: string) => {
    setFileContents(prev => ({ ...prev, [path]: content }));
  };

  const closeFile = (path: string) => {
    setOpenFiles(prev => prev.filter(p => p !== path));
    
    // If the closed file was selected, select another open file or null
    if (selectedFile === path) {
      const remainingFiles = openFiles.filter(p => p !== path);
      setSelectedFile(remainingFiles.length > 0 ? remainingFiles[remainingFiles.length - 1] : null);
    }
    
    // Remove from cache
    setFileContents(prev => {
      const { [path]: removed, ...rest } = prev;
      return rest;
    });
  };

  const watchDirectory = (path: string) => {
    if (connected && !watchedDirectories.includes(path)) {
      emit('filesystem:watch', { path });
    }
  };

  const unwatchDirectory = (path: string) => {
    if (connected && watchedDirectories.includes(path)) {
      emit('filesystem:unwatch', { path });
    }
  };

  const refreshFileTree = (rootPath?: string) => {
    if (connected) {
      // This would make an API call to get the file tree
      fetch(`/api/filesystem/tree?path=${encodeURIComponent(rootPath || process.cwd())}`)
        .then(response => response.json())
        .then(tree => setFileTree(tree))
        .catch(error => console.error('Failed to refresh file tree:', error));
    }
  };

  const analyzeProject = (rootPath?: string) => {
    if (connected) {
      fetch(`/api/filesystem/project?path=${encodeURIComponent(rootPath || process.cwd())}`)
        .then(response => response.json())
        .then(project => setCurrentProject(project))
        .catch(error => console.error('Failed to analyze project:', error));
    }
  };

  const searchFiles = async (query: string, extensions?: string[]): Promise<string[]> => {
    if (!connected) {
      return [];
    }

    try {
      const params = new URLSearchParams({
        query,
        path: process.cwd(),
      });
      
      if (extensions && extensions.length > 0) {
        params.append('extensions', extensions.join(','));
      }

      const response = await fetch(`/api/filesystem/search?${params}`);
      const results = await response.json();
      return results;
    } catch (error) {
      console.error('Search failed:', error);
      return [];
    }
  };

  const value: FileSystemContextType = {
    fileTree,
    currentProject,
    selectedFile,
    openFiles,
    watchedDirectories,
    fileContents,
    readFile,
    writeFile,
    createDirectory,
    deleteFile,
    openFile,
    closeFile,
    setSelectedFile,
    watchDirectory,
    unwatchDirectory,
    refreshFileTree,
    analyzeProject,
    searchFiles,
    updateFileContent,
  };

  return (
    <FileSystemContext.Provider value={value}>
      {children}
    </FileSystemContext.Provider>
  );
}

export function useFileSystem(): FileSystemContextType {
  const context = useContext(FileSystemContext);
  if (context === undefined) {
    throw new Error('useFileSystem must be used within a FileSystemProvider');
  }
  return context;
}
