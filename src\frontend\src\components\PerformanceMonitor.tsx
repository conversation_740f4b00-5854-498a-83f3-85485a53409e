import React, { useState, useEffect, useRef } from 'react';
import { useSystem } from '../contexts/SystemContext';
import { useSocket } from '../contexts/SocketContext';

interface PerformanceMetric {
  timestamp: number;
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}

interface OptimizationSuggestion {
  id: string;
  type: 'performance' | 'memory' | 'cpu' | 'disk' | 'network';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  action: string;
  estimatedImprovement: string;
}

function PerformanceMonitor() {
  const { metrics, isMonitoring } = useSystem();
  const { socket } = useSocket();
  const [performanceHistory, setPerformanceHistory] = useState<PerformanceMetric[]>([]);
  const [suggestions, setSuggestions] = useState<OptimizationSuggestion[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '6h' | '24h'>('1h');
  const [autoOptimize, setAutoOptimize] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Collect performance metrics
  useEffect(() => {
    if (!metrics) return;

    const newMetric: PerformanceMetric = {
      timestamp: Date.now(),
      cpu: metrics.cpu.usage,
      memory: metrics.memory.percentage,
      disk: metrics.disk.percentage,
      network: (metrics.network.upload + metrics.network.download) / 1024 / 1024 // MB/s
    };

    setPerformanceHistory(prev => {
      const updated = [...prev, newMetric];
      // Keep only last 1000 entries
      return updated.slice(-1000);
    });

    // Generate optimization suggestions
    generateOptimizationSuggestions(newMetric);
  }, [metrics]);

  const generateOptimizationSuggestions = (metric: PerformanceMetric) => {
    const newSuggestions: OptimizationSuggestion[] = [];

    // High memory usage suggestions
    if (metric.memory > 80) {
      newSuggestions.push({
        id: 'memory-optimization',
        type: 'memory',
        priority: metric.memory > 90 ? 'critical' : 'high',
        title: 'Memory Optimization Required',
        description: `Memory usage is at ${metric.memory.toFixed(1)}%. This may cause system slowdowns.`,
        impact: 'System responsiveness and application performance',
        action: 'Close unused applications, clear browser cache, restart memory-intensive processes',
        estimatedImprovement: '15-30% memory reduction'
      });
    }

    // High CPU usage suggestions
    if (metric.cpu > 85) {
      newSuggestions.push({
        id: 'cpu-optimization',
        type: 'cpu',
        priority: metric.cpu > 95 ? 'critical' : 'high',
        title: 'CPU Optimization Needed',
        description: `CPU usage is at ${metric.cpu.toFixed(1)}%. Consider optimizing running processes.`,
        impact: 'Overall system performance and battery life',
        action: 'Identify and optimize CPU-intensive processes, enable power saving mode',
        estimatedImprovement: '20-40% CPU usage reduction'
      });
    }

    // Disk space suggestions
    if (metric.disk > 85) {
      newSuggestions.push({
        id: 'disk-optimization',
        type: 'disk',
        priority: metric.disk > 95 ? 'critical' : 'medium',
        title: 'Disk Space Cleanup Required',
        description: `Disk usage is at ${metric.disk.toFixed(1)}%. Free up space to maintain performance.`,
        impact: 'File operations and system stability',
        action: 'Clean temporary files, remove unused applications, move files to external storage',
        estimatedImprovement: '10-50% disk space recovery'
      });
    }

    // Performance trend analysis
    if (performanceHistory.length > 10) {
      const recentMetrics = performanceHistory.slice(-10);
      const avgCpu = recentMetrics.reduce((sum, m) => sum + m.cpu, 0) / recentMetrics.length;
      const avgMemory = recentMetrics.reduce((sum, m) => sum + m.memory, 0) / recentMetrics.length;

      if (avgCpu > 70 && avgMemory > 70) {
        newSuggestions.push({
          id: 'system-optimization',
          type: 'performance',
          priority: 'medium',
          title: 'System Performance Degradation Detected',
          description: 'Sustained high resource usage detected over the last few minutes.',
          impact: 'Overall system performance and user experience',
          action: 'Consider restarting the system or closing resource-intensive applications',
          estimatedImprovement: '25-50% overall performance improvement'
        });
      }
    }

    setSuggestions(newSuggestions);
  };

  const drawPerformanceChart = () => {
    const canvas = canvasRef.current;
    if (!canvas || performanceHistory.length < 2) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const { width, height } = canvas;
    ctx.clearRect(0, 0, width, height);

    // Filter data based on selected time range
    const now = Date.now();
    const timeRangeMs = selectedTimeRange === '1h' ? 3600000 : 
                       selectedTimeRange === '6h' ? 21600000 : 86400000;
    const filteredData = performanceHistory.filter(m => now - m.timestamp <= timeRangeMs);

    if (filteredData.length < 2) return;

    const padding = 40;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;

    // Draw grid
    ctx.strokeStyle = '#374151';
    ctx.lineWidth = 1;
    for (let i = 0; i <= 10; i++) {
      const y = padding + (i * chartHeight) / 10;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // Draw CPU line
    ctx.strokeStyle = '#ef4444';
    ctx.lineWidth = 2;
    ctx.beginPath();
    filteredData.forEach((metric, index) => {
      const x = padding + (index * chartWidth) / (filteredData.length - 1);
      const y = padding + chartHeight - (metric.cpu * chartHeight) / 100;
      if (index === 0) ctx.moveTo(x, y);
      else ctx.lineTo(x, y);
    });
    ctx.stroke();

    // Draw Memory line
    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 2;
    ctx.beginPath();
    filteredData.forEach((metric, index) => {
      const x = padding + (index * chartWidth) / (filteredData.length - 1);
      const y = padding + chartHeight - (metric.memory * chartHeight) / 100;
      if (index === 0) ctx.moveTo(x, y);
      else ctx.lineTo(x, y);
    });
    ctx.stroke();

    // Draw labels
    ctx.fillStyle = '#9ca3af';
    ctx.font = '12px monospace';
    ctx.fillText('CPU', 10, 20);
    ctx.fillStyle = '#ef4444';
    ctx.fillRect(35, 12, 15, 3);
    
    ctx.fillStyle = '#9ca3af';
    ctx.fillText('Memory', 60, 20);
    ctx.fillStyle = '#3b82f6';
    ctx.fillRect(105, 12, 15, 3);
  };

  useEffect(() => {
    drawPerformanceChart();
  }, [performanceHistory, selectedTimeRange]);

  const handleOptimizationAction = async (suggestion: OptimizationSuggestion) => {
    if (socket) {
      socket.emit('system:optimize', {
        type: suggestion.type,
        action: suggestion.action
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-500 bg-red-50 border-red-200';
      case 'high': return 'text-orange-500 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-500 bg-yellow-50 border-yellow-200';
      default: return 'text-blue-500 bg-blue-50 border-blue-200';
    }
  };

  if (!isMonitoring) {
    return (
      <div className="p-6 text-center">
        <div className="text-gray-500 dark:text-gray-400">
          <div className="text-4xl mb-4">📊</div>
          <h3 className="text-lg font-semibold mb-2">Performance Monitoring Disabled</h3>
          <p>Enable system monitoring to view performance insights and optimization suggestions.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Performance Monitor</h2>
        <div className="flex items-center space-x-4">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as '1h' | '6h' | '24h')}
            className="px-3 py-1 border border-gray-300 dark:border-dark-600 rounded-md bg-white dark:bg-dark-700 text-sm"
          >
            <option value="1h">Last Hour</option>
            <option value="6h">Last 6 Hours</option>
            <option value="24h">Last 24 Hours</option>
          </select>
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={autoOptimize}
              onChange={(e) => setAutoOptimize(e.target.checked)}
              className="rounded"
            />
            <span>Auto-optimize</span>
          </label>
        </div>
      </div>

      {/* Performance Chart */}
      <div className="bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 p-4">
        <h3 className="text-lg font-semibold mb-4">Resource Usage Trends</h3>
        <canvas
          ref={canvasRef}
          width={800}
          height={300}
          className="w-full h-64 border border-gray-200 dark:border-dark-600 rounded"
        />
      </div>

      {/* Current Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">CPU Usage</p>
                <p className="text-2xl font-bold">{metrics.cpu.usage.toFixed(1)}%</p>
              </div>
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                metrics.cpu.usage > 80 ? 'bg-red-100 text-red-600' : 
                metrics.cpu.usage > 60 ? 'bg-yellow-100 text-yellow-600' : 
                'bg-green-100 text-green-600'
              }`}>
                🖥️
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Memory Usage</p>
                <p className="text-2xl font-bold">{metrics.memory.percentage.toFixed(1)}%</p>
              </div>
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                metrics.memory.percentage > 80 ? 'bg-red-100 text-red-600' : 
                metrics.memory.percentage > 60 ? 'bg-yellow-100 text-yellow-600' : 
                'bg-green-100 text-green-600'
              }`}>
                🧠
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Disk Usage</p>
                <p className="text-2xl font-bold">{metrics.disk.percentage.toFixed(1)}%</p>
              </div>
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                metrics.disk.percentage > 80 ? 'bg-red-100 text-red-600' : 
                metrics.disk.percentage > 60 ? 'bg-yellow-100 text-yellow-600' : 
                'bg-green-100 text-green-600'
              }`}>
                💾
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Network</p>
                <p className="text-2xl font-bold">{((metrics.network.upload + metrics.network.download) / 1024 / 1024).toFixed(1)} MB/s</p>
              </div>
              <div className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-100 text-blue-600">
                🌐
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Optimization Suggestions */}
      {suggestions.length > 0 && (
        <div className="bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 p-6">
          <h3 className="text-lg font-semibold mb-4">Optimization Suggestions</h3>
          <div className="space-y-4">
            {suggestions.map((suggestion) => (
              <div
                key={suggestion.id}
                className={`p-4 rounded-lg border ${getPriorityColor(suggestion.priority)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium uppercase tracking-wide">
                        {suggestion.priority}
                      </span>
                      <span className="text-xs bg-gray-200 dark:bg-dark-600 px-2 py-1 rounded">
                        {suggestion.type}
                      </span>
                    </div>
                    <h4 className="font-semibold mb-1">{suggestion.title}</h4>
                    <p className="text-sm mb-2">{suggestion.description}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      <strong>Impact:</strong> {suggestion.impact}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      <strong>Action:</strong> {suggestion.action}
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400">
                      <strong>Expected improvement:</strong> {suggestion.estimatedImprovement}
                    </p>
                  </div>
                  <button
                    onClick={() => handleOptimizationAction(suggestion)}
                    className="ml-4 px-3 py-1 bg-nexus-500 text-white rounded-md text-sm hover:bg-nexus-600 transition-colors"
                  >
                    Optimize
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default PerformanceMonitor;
