import React, { useState, useEffect } from 'react';
import { useAI } from '../contexts/AIContext';
import { useSocket } from '../contexts/SocketContext';

interface ProviderConfig {
  id: string;
  name: string;
  apiKey: string;
  baseUrl?: string;
  model?: string;
  enabled: boolean;
}

interface ProviderTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  defaultModel: string;
  baseUrl?: string;
  keyPlaceholder: string;
  models: string[];
}

function AIProviderConfig() {
  const { providers, selectedProvider } = useAI();
  const { socket } = useSocket();
  const [configs, setConfigs] = useState<ProviderConfig[]>([]);
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [testingProvider, setTestingProvider] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, { success: boolean; message: string }>>({});

  const providerTemplates: ProviderTemplate[] = [
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'GPT-4, GPT-3.5 Turbo, and other OpenAI models',
      icon: '🤖',
      defaultModel: 'gpt-4',
      keyPlaceholder: 'sk-...',
      models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-3.5-turbo-16k']
    },
    {
      id: 'anthropic',
      name: 'Anthropic',
      description: 'Claude 3 Opus, Sonnet, and Haiku models',
      icon: '🧠',
      defaultModel: 'claude-3-opus-20240229',
      keyPlaceholder: 'sk-ant-...',
      models: ['claude-3-opus-20240229', 'claude-3-sonnet-20240229', 'claude-3-haiku-20240307']
    },
    {
      id: 'groq',
      name: 'Groq',
      description: 'Ultra-fast inference with Llama and Mixtral models',
      icon: '⚡',
      defaultModel: 'llama2-70b-4096',
      keyPlaceholder: 'gsk_...',
      models: ['llama2-70b-4096', 'mixtral-8x7b-32768', 'gemma-7b-it']
    }
  ];

  useEffect(() => {
    // Load existing configurations
    const savedConfigs = localStorage.getItem('nexus-ai-providers');
    if (savedConfigs) {
      setConfigs(JSON.parse(savedConfigs));
    }
  }, []);

  const saveConfigs = (newConfigs: ProviderConfig[]) => {
    setConfigs(newConfigs);
    localStorage.setItem('nexus-ai-providers', JSON.stringify(newConfigs));
    
    // Send to backend
    if (socket) {
      socket.emit('ai:update-providers', newConfigs);
    }
  };

  const addProvider = (template: ProviderTemplate) => {
    const newConfig: ProviderConfig = {
      id: `${template.id}-${Date.now()}`,
      name: template.name,
      apiKey: '',
      baseUrl: template.baseUrl,
      model: template.defaultModel,
      enabled: false
    };

    const newConfigs = [...configs, newConfig];
    saveConfigs(newConfigs);
    setShowAddProvider(false);
  };

  const updateProvider = (id: string, updates: Partial<ProviderConfig>) => {
    const newConfigs = configs.map(config => 
      config.id === id ? { ...config, ...updates } : config
    );
    saveConfigs(newConfigs);
  };

  const removeProvider = (id: string) => {
    const newConfigs = configs.filter(config => config.id !== id);
    saveConfigs(newConfigs);
  };

  const testProvider = async (config: ProviderConfig) => {
    setTestingProvider(config.id);
    
    try {
      if (socket) {
        socket.emit('ai:test-provider', {
          id: config.id,
          name: config.name,
          apiKey: config.apiKey,
          baseUrl: config.baseUrl,
          model: config.model
        });

        // Listen for test result
        socket.on('ai:test-result', (result) => {
          setTestResults(prev => ({
            ...prev,
            [config.id]: result
          }));
          setTestingProvider(null);
        });
      }
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [config.id]: {
          success: false,
          message: 'Failed to test provider'
        }
      }));
      setTestingProvider(null);
    }
  };

  const getProviderTemplate = (name: string) => {
    return providerTemplates.find(t => t.name === name);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">AI Provider Configuration</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Configure AI providers to enable intelligent assistance features
          </p>
        </div>
        <button
          onClick={() => setShowAddProvider(true)}
          className="px-4 py-2 bg-nexus-500 text-white rounded-lg hover:bg-nexus-600 transition-colors"
        >
          Add Provider
        </button>
      </div>

      {/* Current Providers */}
      <div className="space-y-4">
        {configs.length === 0 ? (
          <div className="text-center py-12 bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="text-lg font-semibold mb-2">No AI Providers Configured</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Add an AI provider to enable intelligent assistance features
            </p>
            <button
              onClick={() => setShowAddProvider(true)}
              className="px-4 py-2 bg-nexus-500 text-white rounded-lg hover:bg-nexus-600 transition-colors"
            >
              Get Started
            </button>
          </div>
        ) : (
          configs.map((config) => {
            const template = getProviderTemplate(config.name);
            const testResult = testResults[config.id];
            
            return (
              <div
                key={config.id}
                className="bg-white dark:bg-dark-800 rounded-lg border border-gray-200 dark:border-dark-700 p-6"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">{template?.icon || '🤖'}</div>
                    <div>
                      <h3 className="text-lg font-semibold">{config.name}</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {template?.description || 'Custom AI provider'}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.enabled}
                        onChange={(e) => updateProvider(config.id, { enabled: e.target.checked })}
                        className="rounded"
                      />
                      <span className="text-sm">Enabled</span>
                    </label>
                    <button
                      onClick={() => removeProvider(config.id)}
                      className="text-red-500 hover:text-red-700 p-1"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium mb-1">API Key</label>
                    <input
                      type="password"
                      value={config.apiKey}
                      onChange={(e) => updateProvider(config.id, { apiKey: e.target.value })}
                      placeholder={template?.keyPlaceholder || 'Enter API key'}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md bg-white dark:bg-dark-700"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Model</label>
                    <select
                      value={config.model || ''}
                      onChange={(e) => updateProvider(config.id, { model: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md bg-white dark:bg-dark-700"
                    >
                      {template?.models.map(model => (
                        <option key={model} value={model}>{model}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {config.baseUrl && (
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-1">Base URL</label>
                    <input
                      type="url"
                      value={config.baseUrl}
                      onChange={(e) => updateProvider(config.id, { baseUrl: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md bg-white dark:bg-dark-700"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <button
                    onClick={() => testProvider(config)}
                    disabled={!config.apiKey || testingProvider === config.id}
                    className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {testingProvider === config.id ? 'Testing...' : 'Test Connection'}
                  </button>

                  {testResult && (
                    <div className={`flex items-center space-x-2 ${
                      testResult.success ? 'text-green-600' : 'text-red-600'
                    }`}>
                      <div className={`w-2 h-2 rounded-full ${
                        testResult.success ? 'bg-green-500' : 'bg-red-500'
                      }`} />
                      <span className="text-sm">{testResult.message}</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Add Provider Modal */}
      {showAddProvider && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-dark-800 rounded-lg p-6 w-full max-w-2xl mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold">Add AI Provider</h3>
              <button
                onClick={() => setShowAddProvider(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {providerTemplates.map((template) => (
                <div
                  key={template.id}
                  onClick={() => addProvider(template)}
                  className="p-4 border border-gray-200 dark:border-dark-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-3xl mb-2">{template.icon}</div>
                    <h4 className="font-semibold mb-1">{template.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{template.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default AIProviderConfig;
