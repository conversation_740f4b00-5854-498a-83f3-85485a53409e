"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PredictiveIntelligence = void 0;
class PredictiveIntelligence {
    constructor(aiOrchestrator, fileSystemService, systemMonitor) {
        this.codePatterns = new Map();
        this.workflowPatterns = new Map();
        this.userBehavior = [];
        this.aiOrchestrator = aiOrchestrator;
        this.fileSystemService = fileSystemService;
        this.systemMonitor = systemMonitor;
        // Start pattern analysis
        this.startPatternAnalysis();
    }
    startPatternAnalysis() {
        // Analyze patterns every 30 seconds
        setInterval(() => {
            this.analyzeCodePatterns();
            this.analyzeWorkflowPatterns();
        }, 30000);
    }
    async analyzeCodebase(rootPath) {
        const insights = [];
        try {
            // Get all code files
            const files = await this.fileSystemService.searchFiles('.', '*.{ts,tsx,js,jsx,py,java,cpp,c,cs}');
            for (const file of files) {
                const content = await this.fileSystemService.readFile(file);
                const fileInsights = await this.analyzeFile(file, content);
                insights.push(...fileInsights);
            }
            // Add workflow insights
            const workflowInsights = await this.generateWorkflowInsights();
            insights.push(...workflowInsights);
            // Add performance insights
            const performanceInsights = await this.generatePerformanceInsights();
            insights.push(...performanceInsights);
        }
        catch (error) {
            console.error('Error analyzing codebase:', error);
        }
        return insights.sort((a, b) => b.confidence - a.confidence);
    }
    async analyzeFile(filePath, content) {
        const insights = [];
        // Detect missing error handling
        if (this.hasMissingErrorHandling(content)) {
            insights.push({
                type: 'error_prevention',
                priority: 'high',
                title: 'Missing Error Handling',
                description: `File ${filePath} contains async operations without proper error handling`,
                suggestion: 'Add try-catch blocks around async operations to prevent crashes',
                confidence: 0.85,
                context: { file: filePath },
                actions: [
                    {
                        label: 'Add Error Handling',
                        action: 'add_error_handling',
                        parameters: { file: filePath }
                    }
                ]
            });
        }
        // Detect performance issues
        if (this.hasPerformanceIssues(content)) {
            insights.push({
                type: 'performance_warning',
                priority: 'medium',
                title: 'Potential Performance Issue',
                description: `File ${filePath} may have performance bottlenecks`,
                suggestion: 'Consider optimizing loops and reducing computational complexity',
                confidence: 0.75,
                context: { file: filePath },
                actions: [
                    {
                        label: 'Optimize Performance',
                        action: 'optimize_performance',
                        parameters: { file: filePath }
                    }
                ]
            });
        }
        // Detect code duplication
        const duplications = this.detectCodeDuplication(content);
        if (duplications.length > 0) {
            insights.push({
                type: 'code_suggestion',
                priority: 'medium',
                title: 'Code Duplication Detected',
                description: `File ${filePath} contains duplicated code patterns`,
                suggestion: 'Extract common functionality into reusable functions',
                confidence: 0.80,
                context: { file: filePath, duplications },
                actions: [
                    {
                        label: 'Refactor Duplicated Code',
                        action: 'refactor_duplication',
                        parameters: { file: filePath, duplications }
                    }
                ]
            });
        }
        return insights;
    }
    hasMissingErrorHandling(content) {
        // Simple heuristic: check for async/await without try-catch
        const asyncPattern = /await\s+[^;]+/g;
        const tryPattern = /try\s*{/g;
        const asyncMatches = content.match(asyncPattern) || [];
        const tryMatches = content.match(tryPattern) || [];
        return asyncMatches.length > 0 && tryMatches.length === 0;
    }
    hasPerformanceIssues(content) {
        // Check for nested loops
        const nestedLoopPattern = /for\s*\([^}]*for\s*\(/g;
        const inefficientPattern = /\.forEach\s*\([^}]*\.forEach\s*\(/g;
        return nestedLoopPattern.test(content) || inefficientPattern.test(content);
    }
    detectCodeDuplication(content) {
        const duplications = [];
        const lines = content.split('\n');
        const lineGroups = new Map();
        // Group similar lines
        lines.forEach((line, index) => {
            const trimmed = line.trim();
            if (trimmed.length > 10) { // Only consider substantial lines
                if (!lineGroups.has(trimmed)) {
                    lineGroups.set(trimmed, []);
                }
                lineGroups.get(trimmed).push(index);
            }
        });
        // Find duplications
        lineGroups.forEach((indices, line) => {
            if (indices.length > 1) {
                duplications.push(line);
            }
        });
        return duplications;
    }
    async generateWorkflowInsights() {
        const insights = [];
        // Analyze recent user behavior
        const recentActions = this.userBehavior.slice(-50);
        const actionSequences = this.extractActionSequences(recentActions);
        for (const sequence of actionSequences) {
            if (sequence.frequency > 3) {
                insights.push({
                    type: 'workflow_optimization',
                    priority: 'medium',
                    title: 'Workflow Pattern Detected',
                    description: `You frequently perform: ${sequence.sequence.join(' → ')}`,
                    suggestion: 'Consider creating a custom command or macro for this workflow',
                    confidence: 0.70,
                    context: { sequence },
                    actions: [
                        {
                            label: 'Create Macro',
                            action: 'create_macro',
                            parameters: { sequence: sequence.sequence }
                        }
                    ]
                });
            }
        }
        return insights;
    }
    async generatePerformanceInsights() {
        const insights = [];
        const metrics = this.systemMonitor.getCurrentMetrics();
        if (metrics) {
            // High memory usage warning
            if (metrics.memory.percentage > 80) {
                insights.push({
                    type: 'performance_warning',
                    priority: 'high',
                    title: 'High Memory Usage',
                    description: `Memory usage is at ${metrics.memory.percentage}%`,
                    suggestion: 'Consider closing unused applications or optimizing memory-intensive code',
                    confidence: 0.95,
                    context: { metrics },
                    actions: [
                        {
                            label: 'Analyze Memory Usage',
                            action: 'analyze_memory',
                            parameters: {}
                        }
                    ]
                });
            }
            // High CPU usage warning
            if (metrics.cpu.usage > 85) {
                insights.push({
                    type: 'performance_warning',
                    priority: 'high',
                    title: 'High CPU Usage',
                    description: `CPU usage is at ${metrics.cpu.usage}%`,
                    suggestion: 'Check for CPU-intensive processes or optimize algorithms',
                    confidence: 0.90,
                    context: { metrics },
                    actions: [
                        {
                            label: 'Analyze CPU Usage',
                            action: 'analyze_cpu',
                            parameters: {}
                        }
                    ]
                });
            }
        }
        return insights;
    }
    extractActionSequences(actions) {
        const sequences = new Map();
        // Simple sequence extraction (sliding window of 3)
        for (let i = 0; i < actions.length - 2; i++) {
            const sequence = actions.slice(i, i + 3).map(a => a.action);
            const key = sequence.join('→');
            if (sequences.has(key)) {
                const pattern = sequences.get(key);
                pattern.frequency++;
                pattern.lastUsed = new Date();
            }
            else {
                sequences.set(key, {
                    id: key,
                    sequence,
                    frequency: 1,
                    lastUsed: new Date(),
                    context: 'user_workflow',
                    nextSteps: []
                });
            }
        }
        return Array.from(sequences.values());
    }
    recordUserAction(action, context) {
        this.userBehavior.push({
            timestamp: new Date(),
            action,
            context
        });
        // Keep only last 1000 actions
        if (this.userBehavior.length > 1000) {
            this.userBehavior = this.userBehavior.slice(-1000);
        }
    }
    analyzeCodePatterns() {
        // This would analyze code patterns over time
        // Implementation would involve more sophisticated pattern recognition
    }
    analyzeWorkflowPatterns() {
        // This would analyze workflow patterns over time
        // Implementation would involve machine learning for pattern recognition
    }
    async getContextualSuggestions(currentFile, cursorPosition, recentCode) {
        try {
            const prompt = `
        Analyze this code context and provide intelligent suggestions:
        
        File: ${currentFile}
        Position: Line ${cursorPosition.line}, Column ${cursorPosition.column}
        Recent code:
        ${recentCode}
        
        Provide 3-5 contextual suggestions for what the developer might want to do next.
        Focus on:
        1. Code completion
        2. Refactoring opportunities
        3. Performance improvements
        4. Error handling
        5. Testing suggestions
      `;
            // For now, return mock suggestions since AIOrchestrator doesn't have processRequest
            // In a real implementation, this would call the AI service
            const mockSuggestions = [
                'Add error handling for this function',
                'Consider extracting this logic into a separate method',
                'Add type annotations for better type safety',
                'Consider using async/await for better readability',
                'Add unit tests for this functionality'
            ];
            return mockSuggestions;
        }
        catch (error) {
            console.error('Error getting contextual suggestions:', error);
            return [];
        }
    }
}
exports.PredictiveIntelligence = PredictiveIntelligence;
