import React, { useState, useRef, useEffect } from 'react';
import { useAI } from '../contexts/AIContext';
import { useSystem } from '../contexts/SystemContext';
import { useFileSystem } from '../contexts/FileSystemContext';
import VoiceCommand from './VoiceCommand';
import AIProviderConfig from './AIProviderConfig';

function AIChat() {
  const [activeTab, setActiveTab] = useState<'chat' | 'config'>('chat');
  const { 
    chatHistory, 
    isLoading, 
    sendMessage, 
    clearHistory, 
    providers, 
    selectedProvider, 
    setSelectedProvider 
  } = useAI();
  const { metrics } = useSystem();
  const { currentFile } = useFileSystem();
  const [message, setMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory]);

  const handleSendMessage = async () => {
    if (!message.trim() || isLoading) return;

    const userMessage = message.trim();
    setMessage('');

    // Add context information
    const context = {
      currentFile: currentFile?.path,
      systemMetrics: metrics,
      timestamp: new Date().toISOString()
    };

    await sendMessage(userMessage, context);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleVoiceCommand = (command: string) => {
    setMessage(command);
    setIsListening(false);
  };

  const formatMessage = (content: string) => {
    // Simple markdown-like formatting
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>');
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* AI Header with Tabs */}
      <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-nexus-500 to-nexus-700 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">NEXUS AI Assistant</h2>
                <p className="text-sm text-gray-600 dark:text-gray-400">Your intelligent development companion</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-dark-700">
          <button
            onClick={() => setActiveTab('chat')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'chat'
                ? 'border-nexus-500 text-nexus-600 dark:text-nexus-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Chat
          </button>
          <button
            onClick={() => setActiveTab('config')}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'config'
                ? 'border-nexus-500 text-nexus-600 dark:text-nexus-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Configuration
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'chat' ? (
        <>
          {/* Chat Messages */}
          <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
            {chatHistory.length === 0 ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center max-w-md">
                  <div className="w-16 h-16 bg-gradient-to-br from-nexus-500 to-nexus-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Welcome to NEXUS AI
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    I'm your intelligent development assistant. I can help you with coding, debugging, 
                    system optimization, and much more. Ask me anything!
                  </p>
                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <button
                      onClick={() => setMessage("Help me optimize my code")}
                      className="p-2 text-left bg-gray-100 dark:bg-dark-700 rounded-md hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors"
                    >
                      💡 Help me optimize my code
                    </button>
                    <button
                      onClick={() => setMessage("Explain this error message")}
                      className="p-2 text-left bg-gray-100 dark:bg-dark-700 rounded-md hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors"
                    >
                      🐛 Explain this error message
                    </button>
                    <button
                      onClick={() => setMessage("Review my system performance")}
                      className="p-2 text-left bg-gray-100 dark:bg-dark-700 rounded-md hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors"
                    >
                      📊 Review my system performance
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {chatHistory.map((msg, index) => (
                  <div
                    key={index}
                    className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        msg.role === 'user'
                          ? 'bg-nexus-500 text-white'
                          : 'bg-gray-100 dark:bg-dark-700 text-gray-900 dark:text-white'
                      }`}
                    >
                      <div
                        dangerouslySetInnerHTML={{
                          __html: formatMessage(msg.content)
                        }}
                      />
                      <div className="text-xs opacity-70 mt-1">
                        {new Date(msg.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                ))}
                {isLoading && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 dark:bg-dark-700 p-3 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-nexus-500"></div>
                        <span className="text-sm text-gray-600 dark:text-gray-400">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </div>
            )}
          </div>

          {/* Chat Input */}
          <div className="border-t border-gray-200 dark:border-dark-700 p-4">
            <div className="flex items-end space-x-3">
              <div className="flex-1">
                <textarea
                  ref={textareaRef}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Ask NEXUS AI anything..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md resize-none focus:ring-2 focus:ring-nexus-500 focus:border-transparent bg-white dark:bg-dark-700 text-gray-900 dark:text-white"
                  rows={3}
                  disabled={isLoading}
                />
              </div>
              <div className="flex flex-col space-y-2">
                <VoiceCommand
                  onCommand={handleVoiceCommand}
                  isListening={isListening}
                  setIsListening={setIsListening}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!message.trim() || isLoading}
                  className="px-4 py-2 bg-nexus-500 text-white rounded-md hover:bg-nexus-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Send
                </button>
              </div>
            </div>
            <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-4">
                <span>
                  Provider: {selectedProvider ? 
                    providers.find(p => p.id === selectedProvider)?.name || 'Unknown' : 
                    'Auto-select'
                  }
                </span>
                <span>Messages: {chatHistory.length}</span>
              </div>
              <span>Press Shift+Enter for new line</span>
            </div>
          </div>
        </>
      ) : (
        <AIProviderConfig />
      )}
    </div>
  );
}

export default AIChat;
