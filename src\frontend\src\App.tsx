import React, { useState, useEffect } from 'react';
import { SocketProvider } from './contexts/SocketContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { AIContextProvider } from './contexts/AIContext';
import { SystemProvider } from './contexts/SystemContext';
import { TerminalProvider } from './contexts/TerminalContext';
import { FileSystemProvider } from './contexts/FileSystemContext';

import Header from './components/Header';
import Sidebar from './components/Sidebar';
import MainContent from './components/MainContent';
import StatusBar from './components/StatusBar';

function App() {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [activePanel, setActivePanel] = useState<'terminal' | 'ai' | 'files' | 'system' | 'analysis' | 'workflow'>('terminal');

  return (
    <ThemeProvider>
      <SocketProvider>
        <SystemProvider>
          <AIContextProvider>
            <TerminalProvider>
              <FileSystemProvider>
                <div className="h-screen flex flex-col bg-gray-50 dark:bg-dark-900">
                  {/* Header */}
                  <Header 
                    onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
                    sidebarCollapsed={sidebarCollapsed}
                  />
                  
                  {/* Main Layout */}
                  <div className="flex-1 flex overflow-hidden">
                    {/* Sidebar */}
                    <Sidebar 
                      collapsed={sidebarCollapsed}
                      activePanel={activePanel}
                      onPanelChange={setActivePanel}
                    />
                    
                    {/* Main Content Area */}
                    <MainContent 
                      activePanel={activePanel}
                      sidebarCollapsed={sidebarCollapsed}
                    />
                  </div>
                  
                  {/* Status Bar */}
                  <StatusBar />
                </div>
              </FileSystemProvider>
            </TerminalProvider>
          </AIContextProvider>
        </SystemProvider>
      </SocketProvider>
    </ThemeProvider>
  );
}

export default App;
