import { SystemMonitor } from './SystemMonitor';
import { FileSystemService } from './FileSystemService';
import { AIOrchestrator } from './AIOrchestrator';

interface PerformanceMetric {
  timestamp: Date;
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}

interface OptimizationSuggestion {
  id: string;
  type: 'memory' | 'cpu' | 'disk' | 'network' | 'code' | 'system';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  effort: 'low' | 'medium' | 'high';
  actions: Array<{
    description: string;
    command?: string;
    automated: boolean;
  }>;
  estimatedImprovement: number; // percentage
}

interface PerformanceProfile {
  baseline: PerformanceMetric;
  current: PerformanceMetric;
  trend: 'improving' | 'degrading' | 'stable';
  bottlenecks: string[];
  recommendations: OptimizationSuggestion[];
}

export class PerformanceOptimizer {
  private systemMonitor: SystemMonitor;
  private fileSystemService: FileSystemService;
  private aiOrchestrator: AIOrchestrator;
  private metricsHistory: PerformanceMetric[] = [];
  private optimizationHistory: Map<string, Date> = new Map();

  constructor(
    systemMonitor: SystemMonitor,
    fileSystemService: FileSystemService,
    aiOrchestrator: AIOrchestrator
  ) {
    this.systemMonitor = systemMonitor;
    this.fileSystemService = fileSystemService;
    this.aiOrchestrator = aiOrchestrator;
    
    // Start performance monitoring
    this.startMonitoring();
  }

  private startMonitoring() {
    // Collect metrics every 30 seconds
    setInterval(() => {
      this.collectMetrics();
    }, 30000);

    // Analyze performance every 5 minutes
    setInterval(() => {
      this.analyzePerformance();
    }, 300000);
  }

  private collectMetrics() {
    const metrics = this.systemMonitor.getCurrentMetrics();
    if (metrics) {
      const performanceMetric: PerformanceMetric = {
        timestamp: new Date(),
        cpu: metrics.cpu.usage,
        memory: metrics.memory.percentage,
        disk: metrics.disk.percentage,
        network: metrics.network.latency
      };

      this.metricsHistory.push(performanceMetric);
      
      // Keep only last 1000 metrics (about 8 hours at 30s intervals)
      if (this.metricsHistory.length > 1000) {
        this.metricsHistory = this.metricsHistory.slice(-1000);
      }
    }
  }

  public async analyzePerformance(): Promise<PerformanceProfile> {
    if (this.metricsHistory.length < 2) {
      throw new Error('Insufficient metrics data for analysis');
    }

    const current = this.metricsHistory[this.metricsHistory.length - 1];
    const baseline = this.calculateBaseline();
    const trend = this.calculateTrend();
    const bottlenecks = this.identifyBottlenecks();
    const recommendations = await this.generateRecommendations();

    return {
      baseline,
      current,
      trend,
      bottlenecks,
      recommendations
    };
  }

  private calculateBaseline(): PerformanceMetric {
    if (this.metricsHistory.length === 0) {
      return {
        timestamp: new Date(),
        cpu: 0,
        memory: 0,
        disk: 0,
        network: 0
      };
    }

    // Calculate average of last 100 metrics
    const recentMetrics = this.metricsHistory.slice(-100);
    const avg = recentMetrics.reduce(
      (acc, metric) => ({
        cpu: acc.cpu + metric.cpu,
        memory: acc.memory + metric.memory,
        disk: acc.disk + metric.disk,
        network: acc.network + metric.network
      }),
      { cpu: 0, memory: 0, disk: 0, network: 0 }
    );

    return {
      timestamp: new Date(),
      cpu: avg.cpu / recentMetrics.length,
      memory: avg.memory / recentMetrics.length,
      disk: avg.disk / recentMetrics.length,
      network: avg.network / recentMetrics.length
    };
  }

  private calculateTrend(): 'improving' | 'degrading' | 'stable' {
    if (this.metricsHistory.length < 10) return 'stable';

    const recent = this.metricsHistory.slice(-10);
    const older = this.metricsHistory.slice(-20, -10);

    const recentAvg = recent.reduce((acc, m) => acc + m.cpu + m.memory, 0) / recent.length;
    const olderAvg = older.reduce((acc, m) => acc + m.cpu + m.memory, 0) / older.length;

    const change = ((recentAvg - olderAvg) / olderAvg) * 100;

    if (change > 5) return 'degrading';
    if (change < -5) return 'improving';
    return 'stable';
  }

  private identifyBottlenecks(): string[] {
    const bottlenecks: string[] = [];
    const current = this.metricsHistory[this.metricsHistory.length - 1];

    if (current.cpu > 80) {
      bottlenecks.push('High CPU usage detected');
    }
    if (current.memory > 85) {
      bottlenecks.push('High memory usage detected');
    }
    if (current.disk > 90) {
      bottlenecks.push('Low disk space detected');
    }
    if (current.network > 1000) {
      bottlenecks.push('High network latency detected');
    }

    return bottlenecks;
  }

  private async generateRecommendations(): Promise<OptimizationSuggestion[]> {
    const recommendations: OptimizationSuggestion[] = [];
    const current = this.metricsHistory[this.metricsHistory.length - 1];

    // Memory optimization
    if (current.memory > 80) {
      recommendations.push({
        id: 'memory-optimization',
        type: 'memory',
        priority: current.memory > 90 ? 'critical' : 'high',
        title: 'Optimize Memory Usage',
        description: `Memory usage is at ${current.memory.toFixed(1)}%. This may cause system slowdowns.`,
        impact: 'Reduce memory usage by 20-40%',
        effort: 'medium',
        actions: [
          {
            description: 'Close unused applications',
            automated: false
          },
          {
            description: 'Clear browser cache and tabs',
            automated: false
          },
          {
            description: 'Restart memory-intensive processes',
            automated: true
          }
        ],
        estimatedImprovement: 25
      });
    }

    // CPU optimization
    if (current.cpu > 75) {
      recommendations.push({
        id: 'cpu-optimization',
        type: 'cpu',
        priority: current.cpu > 90 ? 'critical' : 'high',
        title: 'Optimize CPU Usage',
        description: `CPU usage is at ${current.cpu.toFixed(1)}%. This may cause performance issues.`,
        impact: 'Reduce CPU load and improve responsiveness',
        effort: 'medium',
        actions: [
          {
            description: 'Identify and optimize CPU-intensive processes',
            automated: false
          },
          {
            description: 'Adjust process priorities',
            command: 'renice -n 10 -p <pid>',
            automated: true
          }
        ],
        estimatedImprovement: 30
      });
    }

    // Disk optimization
    if (current.disk > 85) {
      recommendations.push({
        id: 'disk-optimization',
        type: 'disk',
        priority: current.disk > 95 ? 'critical' : 'high',
        title: 'Free Up Disk Space',
        description: `Disk usage is at ${current.disk.toFixed(1)}%. Low disk space can severely impact performance.`,
        impact: 'Free up disk space and improve I/O performance',
        effort: 'low',
        actions: [
          {
            description: 'Clean temporary files',
            command: 'npm cache clean --force',
            automated: true
          },
          {
            description: 'Remove unused node_modules',
            command: 'find . -name "node_modules" -type d -prune -exec du -sh {} \\;',
            automated: false
          },
          {
            description: 'Clear system cache',
            automated: false
          }
        ],
        estimatedImprovement: 15
      });
    }

    // Code optimization suggestions
    try {
      const codeOptimizations = await this.analyzeCodePerformance();
      recommendations.push(...codeOptimizations);
    } catch (error) {
      console.error('Failed to analyze code performance:', error);
    }

    return recommendations;
  }

  private async analyzeCodePerformance(): Promise<OptimizationSuggestion[]> {
    const suggestions: OptimizationSuggestion[] = [];

    try {
      // Analyze JavaScript/TypeScript files for performance issues
      const jsFiles = await this.fileSystemService.searchFiles('.', {
        extensions: ['.js', '.ts', '.jsx', '.tsx'],
        maxResults: 50
      });

      for (const file of jsFiles.slice(0, 10)) { // Limit to 10 files for performance
        try {
          const content = await this.fileSystemService.readFile(file);
          const issues = this.detectPerformanceIssues(content, file);
          suggestions.push(...issues);
        } catch (error) {
          console.error(`Failed to analyze file ${file}:`, error);
        }
      }
    } catch (error) {
      console.error('Failed to search for files:', error);
    }

    return suggestions;
  }

  private detectPerformanceIssues(content: string, filePath: string): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // Detect inefficient loops
    if (content.includes('for') && content.includes('forEach')) {
      suggestions.push({
        id: `loop-optimization-${filePath}`,
        type: 'code',
        priority: 'medium',
        title: 'Optimize Loop Performance',
        description: `File ${filePath} contains potentially inefficient loops`,
        impact: 'Improve execution speed by 10-50%',
        effort: 'medium',
        actions: [
          {
            description: 'Replace forEach with for loops for better performance',
            automated: false
          },
          {
            description: 'Consider using map/filter/reduce for functional operations',
            automated: false
          }
        ],
        estimatedImprovement: 20
      });
    }

    // Detect large bundle size indicators
    if (content.includes('import') && content.split('import').length > 20) {
      suggestions.push({
        id: `bundle-optimization-${filePath}`,
        type: 'code',
        priority: 'medium',
        title: 'Optimize Bundle Size',
        description: `File ${filePath} has many imports which may increase bundle size`,
        impact: 'Reduce bundle size and improve load times',
        effort: 'high',
        actions: [
          {
            description: 'Use dynamic imports for code splitting',
            automated: false
          },
          {
            description: 'Remove unused imports',
            automated: true
          }
        ],
        estimatedImprovement: 15
      });
    }

    return suggestions;
  }

  public async executeOptimization(suggestionId: string): Promise<boolean> {
    const lastExecution = this.optimizationHistory.get(suggestionId);
    
    // Prevent executing the same optimization too frequently
    if (lastExecution && Date.now() - lastExecution.getTime() < 3600000) { // 1 hour
      throw new Error('Optimization was executed recently. Please wait before retrying.');
    }

    try {
      // This would contain the actual optimization logic
      // For now, we'll simulate the execution
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      this.optimizationHistory.set(suggestionId, new Date());
      return true;
    } catch (error) {
      console.error(`Failed to execute optimization ${suggestionId}:`, error);
      return false;
    }
  }

  public getPerformanceInsights(): any {
    if (this.metricsHistory.length === 0) {
      return null;
    }

    const current = this.metricsHistory[this.metricsHistory.length - 1];
    const baseline = this.calculateBaseline();
    
    return {
      current,
      baseline,
      trend: this.calculateTrend(),
      bottlenecks: this.identifyBottlenecks(),
      healthScore: this.calculateHealthScore(current)
    };
  }

  private calculateHealthScore(metric: PerformanceMetric): number {
    // Calculate a health score from 0-100 based on system metrics
    const cpuScore = Math.max(0, 100 - metric.cpu);
    const memoryScore = Math.max(0, 100 - metric.memory);
    const diskScore = Math.max(0, 100 - metric.disk);
    const networkScore = Math.max(0, 100 - Math.min(100, metric.network / 10));
    
    return Math.round((cpuScore + memoryScore + diskScore + networkScore) / 4);
  }
}
