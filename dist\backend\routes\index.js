"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupRoutes = setupRoutes;
function setupRoutes(app, services) {
    const { aiOrchestrator, systemMonitor, terminalManager, fileSystemService, predictiveIntelligence } = services;
    // Health check
    app.get('/api/health', (req, res) => {
        res.json({
            status: 'ok',
            timestamp: Date.now(),
            services: {
                ai: aiOrchestrator.getAvailableProviders().length > 0,
                system: systemMonitor.isMonitoring(),
                terminal: terminalManager.getAllSessions().length,
                filesystem: true,
            },
        });
    });
    // AI Routes
    app.post('/api/ai/query', async (req, res) => {
        try {
            const { query, context, provider, priority = 'medium' } = req.body;
            const request = {
                id: Math.random().toString(36).substr(2, 9),
                query,
                context: context || {
                    currentFile: undefined,
                    openFiles: [],
                    projectType: 'unknown',
                    recentCommands: [],
                    systemState: systemMonitor.getCurrentMetrics() || {
                        cpu: { usage: 0, temperature: 0, cores: 0 },
                        memory: { total: 0, used: 0, free: 0, percentage: 0 },
                        disk: { total: 0, used: 0, free: 0, percentage: 0 },
                        network: { upload: 0, download: 0, latency: 0 },
                        timestamp: Date.now(),
                    },
                },
                provider,
                priority,
                timestamp: Date.now(),
            };
            const response = await aiOrchestrator.routeQuery(request);
            res.json(response);
        }
        catch (error) {
            console.error('AI query error:', error);
            res.status(500).json({ error: 'AI query failed', message: error.message });
        }
    });
    app.get('/api/ai/providers', (req, res) => {
        res.json(aiOrchestrator.getAvailableProviders());
    });
    app.get('/api/ai/history', (req, res) => {
        const { limit = 50 } = req.query;
        res.json({
            requests: aiOrchestrator.getRequestHistory().slice(-Number(limit)),
            responses: aiOrchestrator.getResponseHistory().slice(-Number(limit)),
        });
    });
    // System Routes
    app.get('/api/system/metrics', (req, res) => {
        const metrics = systemMonitor.getCurrentMetrics();
        if (!metrics) {
            return res.status(503).json({ error: 'System monitoring not available' });
        }
        res.json(metrics);
    });
    app.get('/api/system/metrics/history', (req, res) => {
        const { limit = 100, timeRange } = req.query;
        if (timeRange) {
            const timeRangeMs = Number(timeRange) * 1000; // Convert seconds to ms
            const avgMetrics = systemMonitor.getAverageMetrics(timeRangeMs);
            res.json(avgMetrics);
        }
        else {
            const history = systemMonitor.getMetricsHistory(Number(limit));
            res.json(history);
        }
    });
    app.get('/api/system/processes', async (req, res) => {
        try {
            const processes = await systemMonitor.getProcessList();
            res.json(processes);
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to get process list', message: error.message });
        }
    });
    app.get('/api/system/info', async (req, res) => {
        try {
            const info = await systemMonitor.getSystemInfo();
            res.json(info);
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to get system info', message: error.message });
        }
    });
    // Terminal Routes
    app.post('/api/terminal/sessions', (req, res) => {
        try {
            const { name, workingDirectory } = req.body;
            const session = terminalManager.createSession(name, workingDirectory);
            res.json(session);
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to create terminal session', message: error.message });
        }
    });
    app.get('/api/terminal/sessions', (req, res) => {
        res.json(terminalManager.getAllSessions());
    });
    app.get('/api/terminal/sessions/:id', (req, res) => {
        const session = terminalManager.getSession(req.params.id);
        if (!session) {
            return res.status(404).json({ error: 'Session not found' });
        }
        res.json(session);
    });
    app.post('/api/terminal/sessions/:id/commands', (req, res) => {
        try {
            const { command } = req.body;
            const block = terminalManager.executeCommand(req.params.id, command);
            res.json(block);
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to execute command', message: error.message });
        }
    });
    app.get('/api/terminal/sessions/:id/history', (req, res) => {
        const { limit = 50 } = req.query;
        const history = terminalManager.getCommandHistory(req.params.id, Number(limit));
        res.json(history);
    });
    app.get('/api/terminal/sessions/:id/stats', (req, res) => {
        const stats = terminalManager.getSessionStats(req.params.id);
        if (!stats) {
            return res.status(404).json({ error: 'Session not found' });
        }
        res.json(stats);
    });
    app.delete('/api/terminal/sessions/:id', (req, res) => {
        try {
            terminalManager.closeSession(req.params.id);
            res.json({ success: true });
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to close session', message: error.message });
        }
    });
    // File System Routes
    app.get('/api/filesystem/tree', async (req, res) => {
        try {
            const { path: rootPath = process.cwd(), depth = 3 } = req.query;
            const tree = await fileSystemService.getDirectoryTree(String(rootPath), Number(depth));
            res.json(tree);
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to get directory tree', message: error.message });
        }
    });
    app.get('/api/filesystem/file', async (req, res) => {
        try {
            const { path: filePath } = req.query;
            if (!filePath) {
                return res.status(400).json({ error: 'File path is required' });
            }
            const content = await fileSystemService.readFile(String(filePath));
            res.json({ content });
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to read file', message: error.message });
        }
    });
    app.post('/api/filesystem/file', async (req, res) => {
        try {
            const { path: filePath, content } = req.body;
            await fileSystemService.writeFile(filePath, content);
            res.json({ success: true });
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to write file', message: error.message });
        }
    });
    app.delete('/api/filesystem/file', async (req, res) => {
        try {
            const { path: filePath } = req.query;
            if (!filePath) {
                return res.status(400).json({ error: 'File path is required' });
            }
            await fileSystemService.deleteFile(String(filePath));
            res.json({ success: true });
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to delete file', message: error.message });
        }
    });
    app.post('/api/filesystem/directory', async (req, res) => {
        try {
            const { path: dirPath } = req.body;
            await fileSystemService.createDirectory(dirPath);
            res.json({ success: true });
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to create directory', message: error.message });
        }
    });
    app.get('/api/filesystem/project', async (req, res) => {
        try {
            const { path: rootPath = process.cwd() } = req.query;
            const project = await fileSystemService.analyzeProject(String(rootPath));
            res.json(project);
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to analyze project', message: error.message });
        }
    });
    app.get('/api/filesystem/search', async (req, res) => {
        try {
            const { path: rootPath = process.cwd(), query, extensions } = req.query;
            if (!query) {
                return res.status(400).json({ error: 'Search query is required' });
            }
            const extensionList = extensions ? String(extensions).split(',') : undefined;
            const results = await fileSystemService.searchFiles(String(rootPath), String(query), extensionList);
            res.json(results);
        }
        catch (error) {
            res.status(500).json({ error: 'Failed to search files', message: error.message });
        }
    });
    // Predictive Intelligence Routes
    app.get('/api/intelligence/analyze', async (req, res) => {
        try {
            const { path } = req.query;
            const insights = await predictiveIntelligence.analyzeCodebase(path || process.cwd());
            res.json(insights);
        }
        catch (error) {
            console.error('Intelligence analysis error:', error);
            res.status(500).json({ error: 'Failed to analyze codebase', message: error.message });
        }
    });
    app.post('/api/intelligence/suggestions', async (req, res) => {
        try {
            const { currentFile, cursorPosition, recentCode } = req.body;
            const suggestions = await predictiveIntelligence.getContextualSuggestions(currentFile, cursorPosition, recentCode);
            res.json(suggestions);
        }
        catch (error) {
            console.error('Suggestions error:', error);
            res.status(500).json({ error: 'Failed to get suggestions', message: error.message });
        }
    });
    app.post('/api/intelligence/action', async (req, res) => {
        try {
            const { action, context } = req.body;
            predictiveIntelligence.recordUserAction(action, context);
            res.json({ success: true });
        }
        catch (error) {
            console.error('Action recording error:', error);
            res.status(500).json({ error: 'Failed to record action', message: error.message });
        }
    });
    // Catch-all for frontend routing
    app.get('*', (req, res) => {
        res.sendFile('index.html', { root: '../frontend' });
    });
}
