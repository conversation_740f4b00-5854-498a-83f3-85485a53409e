import React, { useState, useEffect } from 'react';
import { useSystem } from '../contexts/SystemContext';
import { useAI } from '../contexts/AIContext';

interface SystemAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  action?: string;
}

function SystemDashboard() {
  const { metrics, processes, systemInfo, isMonitoring } = useSystem();
  const { sendMessage } = useAI();
  const [alerts, setAlerts] = useState<SystemAlert[]>([]);
  const [showInsights, setShowInsights] = useState(false);

  // Monitor system metrics and generate alerts
  useEffect(() => {
    if (!metrics) return;

    const newAlerts: SystemAlert[] = [];

    // High memory usage alert
    if (metrics.memory.percentage > 85) {
      newAlerts.push({
        id: 'memory-high',
        type: 'warning',
        title: 'High Memory Usage',
        message: `Memory usage is at ${metrics.memory.percentage}%. Consider closing unused applications.`,
        timestamp: new Date(),
        action: 'optimize_memory'
      });
    }

    // High CPU usage alert
    if (metrics.cpu.usage > 90) {
      newAlerts.push({
        id: 'cpu-high',
        type: 'error',
        title: 'High CPU Usage',
        message: `CPU usage is at ${metrics.cpu.usage}%. System may become unresponsive.`,
        timestamp: new Date(),
        action: 'analyze_cpu'
      });
    }

    // Low disk space alert
    if (metrics.disk.percentage > 90) {
      newAlerts.push({
        id: 'disk-low',
        type: 'warning',
        title: 'Low Disk Space',
        message: `Disk usage is at ${metrics.disk.percentage}%. Consider cleaning up files.`,
        timestamp: new Date(),
        action: 'cleanup_disk'
      });
    }

    // High CPU temperature alert
    if (metrics.cpu.temperature > 80) {
      newAlerts.push({
        id: 'temp-high',
        type: 'error',
        title: 'High CPU Temperature',
        message: `CPU temperature is ${metrics.cpu.temperature}°C. Check cooling system.`,
        timestamp: new Date(),
        action: 'check_cooling'
      });
    }

    setAlerts(newAlerts);
  }, [metrics]);

  const handleAIAnalysis = async (type: string) => {
    const context = {
      currentFile: null,
      openFiles: [],
      projectType: 'system-analysis',
      recentCommands: [],
      systemState: metrics,
    };

    let message = '';
    switch (type) {
      case 'performance':
        message = 'Analyze current system performance and suggest optimizations';
        break;
      case 'memory':
        message = 'Help me optimize memory usage and identify memory leaks';
        break;
      case 'cpu':
        message = 'Analyze high CPU usage and suggest solutions';
        break;
      case 'disk':
        message = 'Help me clean up disk space and optimize storage';
        break;
      default:
        message = 'Provide general system health analysis and recommendations';
    }

    await sendMessage(message, context);
    setShowInsights(true);
  };

  if (!isMonitoring || !metrics) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-4 border-nexus-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Loading System Data</h3>
          <p className="text-gray-600 dark:text-gray-400">
            Initializing system monitoring...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6 overflow-y-auto custom-scrollbar">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">System Dashboard</h1>
              <p className="text-gray-600 dark:text-gray-400">
                Real-time monitoring of your development environment
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleAIAnalysis('performance')}
                className="btn btn-primary"
              >
                🤖 AI Analysis
              </button>
              <button
                onClick={() => setShowInsights(!showInsights)}
                className="btn btn-secondary"
              >
                📊 Insights
              </button>
            </div>
          </div>
        </div>

        {/* System Alerts */}
        {alerts.length > 0 && (
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Alerts</h2>
            <div className="space-y-3">
              {alerts.map((alert) => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border-l-4 ${
                    alert.type === 'error'
                      ? 'bg-red-50 dark:bg-red-900/20 border-red-500'
                      : alert.type === 'warning'
                      ? 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500'
                      : 'bg-blue-50 dark:bg-blue-900/20 border-blue-500'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className={`text-sm font-medium ${
                          alert.type === 'error'
                            ? 'text-red-800 dark:text-red-200'
                            : alert.type === 'warning'
                            ? 'text-yellow-800 dark:text-yellow-200'
                            : 'text-blue-800 dark:text-blue-200'
                        }`}>
                          {alert.title}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {alert.timestamp.toLocaleTimeString()}
                        </span>
                      </div>
                      <p className={`text-sm mt-1 ${
                        alert.type === 'error'
                          ? 'text-red-700 dark:text-red-300'
                          : alert.type === 'warning'
                          ? 'text-yellow-700 dark:text-yellow-300'
                          : 'text-blue-700 dark:text-blue-300'
                      }`}>
                        {alert.message}
                      </p>
                    </div>
                    {alert.action && (
                      <button
                        onClick={() => handleAIAnalysis(alert.action!)}
                        className={`ml-4 px-3 py-1 text-xs rounded ${
                          alert.type === 'error'
                            ? 'bg-red-600 text-white hover:bg-red-700'
                            : alert.type === 'warning'
                            ? 'bg-yellow-600 text-white hover:bg-yellow-700'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                      >
                        Fix with AI
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* CPU Card */}
          <div className="metric-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                  </svg>
                </div>
                <span className="font-medium text-gray-900 dark:text-white">CPU</span>
              </div>
              <span className="metric-value text-blue-600 dark:text-blue-400">{metrics.cpu.usage}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className={`progress-fill ${
                  metrics.cpu.usage > 80 ? 'high' : metrics.cpu.usage > 60 ? 'medium' : 'low'
                }`}
                style={{ width: `${metrics.cpu.usage}%` }}
              ></div>
            </div>
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {metrics.cpu.cores} cores • {metrics.cpu.temperature > 0 ? `${metrics.cpu.temperature}°C` : 'Temp N/A'}
            </div>
          </div>

          {/* Memory Card */}
          <div className="metric-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                  </svg>
                </div>
                <span className="font-medium text-gray-900 dark:text-white">Memory</span>
              </div>
              <span className="metric-value text-green-600 dark:text-green-400">{metrics.memory.percentage}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className={`progress-fill ${
                  metrics.memory.percentage > 80 ? 'high' : metrics.memory.percentage > 60 ? 'medium' : 'low'
                }`}
                style={{ width: `${metrics.memory.percentage}%` }}
              ></div>
            </div>
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {(metrics.memory.used / 1024 / 1024 / 1024).toFixed(1)}GB / {(metrics.memory.total / 1024 / 1024 / 1024).toFixed(1)}GB
            </div>
          </div>

          {/* Disk Card */}
          <div className="metric-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                  </svg>
                </div>
                <span className="font-medium text-gray-900 dark:text-white">Disk</span>
              </div>
              <span className="metric-value text-purple-600 dark:text-purple-400">{metrics.disk.percentage}%</span>
            </div>
            <div className="progress-bar">
              <div 
                className={`progress-fill ${
                  metrics.disk.percentage > 80 ? 'high' : metrics.disk.percentage > 60 ? 'medium' : 'low'
                }`}
                style={{ width: `${metrics.disk.percentage}%` }}
              ></div>
            </div>
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              {(metrics.disk.used / 1024 / 1024 / 1024).toFixed(1)}GB / {(metrics.disk.total / 1024 / 1024 / 1024).toFixed(1)}GB
            </div>
          </div>

          {/* Network Card */}
          <div className="metric-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                  </svg>
                </div>
                <span className="font-medium text-gray-900 dark:text-white">Network</span>
              </div>
              <span className="metric-value text-orange-600 dark:text-orange-400">
                {metrics.network.latency}ms
              </span>
            </div>
            <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
              <span>↑ {(metrics.network.upload / 1024).toFixed(1)} KB/s</span>
              <span>↓ {(metrics.network.download / 1024).toFixed(1)} KB/s</span>
            </div>
          </div>
        </div>

        {/* System Information */}
        {systemInfo && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-dark-800 p-6 rounded-lg border border-gray-200 dark:border-dark-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Information</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Platform:</span>
                  <span className="font-mono text-sm">{systemInfo.os?.platform}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">OS:</span>
                  <span className="font-mono text-sm">{systemInfo.os?.distro}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Architecture:</span>
                  <span className="font-mono text-sm">{systemInfo.os?.arch}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">CPU:</span>
                  <span className="font-mono text-sm">{systemInfo.cpu?.brand}</span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-dark-800 p-6 rounded-lg border border-gray-200 dark:border-dark-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Top Processes</h3>
              <div className="space-y-2">
                {processes.slice(0, 5).map((process, index) => (
                  <div key={process.pid} className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-2">
                      <span className="w-6 text-center text-gray-500">#{index + 1}</span>
                      <span className="font-mono">{process.name}</span>
                    </div>
                    <div className="flex items-center space-x-3 text-gray-600 dark:text-gray-400">
                      <span>{process.cpu.toFixed(1)}%</span>
                      <span>{process.memory.toFixed(1)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Real-time Chart Placeholder */}
        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg border border-gray-200 dark:border-dark-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance History</h3>
          <div className="h-64 flex items-center justify-center text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p>Real-time charts will be implemented in Stage 2</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SystemDashboard;
