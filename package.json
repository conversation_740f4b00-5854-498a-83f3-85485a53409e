{"name": "nexus-ai", "version": "0.1.0", "type": "module", "description": "NEXUS AI - Unified Development Environment with JARVIS-like intelligence", "main": "dist/main.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "nodemon", "dev:frontend": "vite", "build": "npm run build:backend && npm run build:frontend", "build:backend": "tsc -p tsconfig.backend.json", "build:frontend": "vite build", "start": "node dist/main.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix"}, "keywords": ["ai", "development", "terminal", "jarvis", "warp", "coding-assistant"], "author": "NEXUS AI Team", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.9.1", "@monaco-editor/react": "^4.7.0", "chokidar": "^3.5.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "groq-sdk": "^0.3.3", "monaco-editor": "^0.52.2", "openai": "^4.20.1", "react": "^18.2.0", "react-dom": "^18.2.0", "simple-git": "^3.20.0", "socket.io": "^4.7.2", "socket.io-client": "^4.8.1", "systeminformation": "^5.21.15", "ws": "^8.14.2"}, "devDependencies": {"@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/jest": "^29.5.6", "@types/node": "^20.8.7", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@types/ws": "^8.5.8", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^8.52.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "ts-node": "^10.9.1", "typescript": "^5.2.2", "vite": "^4.5.0"}}