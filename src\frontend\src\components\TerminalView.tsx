import React, { useState, useRef, useEffect } from 'react';
import { useTerminal } from '../contexts/TerminalContext';
import { useAI } from '../contexts/AIContext';
import { useFileSystem } from '../contexts/FileSystemContext';

function TerminalView() {
  const { sessions, activeSessionId, executeCommand, createSession } = useTerminal();
  const { sendMessage } = useAI();
  const { selectedFile, openFiles } = useFileSystem();
  const [currentCommand, setCurrentCommand] = useState('');
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [commandSuggestions, setCommandSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const terminalRef = useRef<HTMLDivElement>(null);

  const activeSession = sessions.find(s => s.id === activeSessionId);

  useEffect(() => {
    // Auto-scroll to bottom when new output is added
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [activeSession?.blocks]);

  useEffect(() => {
    // Focus input when component mounts or active session changes
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [activeSessionId]);

  useEffect(() => {
    // Generate intelligent command suggestions based on context
    const generateSuggestions = () => {
      const suggestions: string[] = [];

      // File-based suggestions
      if (selectedFile) {
        const ext = selectedFile.split('.').pop()?.toLowerCase();
        if (ext === 'js' || ext === 'ts' || ext === 'jsx' || ext === 'tsx') {
          suggestions.push('npm run dev', 'npm test', 'npm run build', 'npm install');
        } else if (ext === 'py') {
          suggestions.push('python ' + selectedFile.split('/').pop(), 'pip install -r requirements.txt');
        } else if (ext === 'java') {
          suggestions.push('javac ' + selectedFile.split('/').pop(), 'java ' + selectedFile.split('/').pop()?.replace('.java', ''));
        }
      }

      // Git suggestions
      suggestions.push('git status', 'git add .', 'git commit -m ""', 'git push', 'git pull');

      // Common development commands
      suggestions.push('ls -la', 'pwd', 'cd ..', 'mkdir', 'touch', 'code .', 'clear');

      // Docker suggestions
      suggestions.push('docker ps', 'docker build .', 'docker-compose up');

      setCommandSuggestions(suggestions);
    };

    generateSuggestions();
  }, [selectedFile, openFiles]);

  const handleCommandSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!currentCommand.trim()) return;
    
    if (!activeSession) {
      createSession();
      return;
    }

    // Add to command history
    setCommandHistory(prev => [...prev, currentCommand]);
    setHistoryIndex(-1);

    // Check if it's an AI command
    if (currentCommand.startsWith('ai ') || currentCommand.startsWith('nexus ')) {
      const aiQuery = currentCommand.replace(/^(ai|nexus)\s+/, '');
      sendMessage(aiQuery, {
        currentFile: undefined,
        openFiles: [],
        projectType: 'unknown',
        recentCommands: commandHistory.slice(-5),
      });
    } else {
      // Execute regular command
      executeCommand(activeSession.id, currentCommand);
    }

    setCurrentCommand('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (showSuggestions) {
      if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestion(prev => Math.max(0, prev - 1));
        return;
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestion(prev => Math.min(commandSuggestions.length - 1, prev + 1));
        return;
      } else if (e.key === 'Tab' || e.key === 'Enter') {
        e.preventDefault();
        setCurrentCommand(commandSuggestions[selectedSuggestion]);
        setShowSuggestions(false);
        return;
      } else if (e.key === 'Escape') {
        setShowSuggestions(false);
        return;
      }
    }

    if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (historyIndex < commandHistory.length - 1) {
        const newIndex = historyIndex + 1;
        setHistoryIndex(newIndex);
        setCurrentCommand(commandHistory[commandHistory.length - 1 - newIndex]);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (historyIndex > 0) {
        const newIndex = historyIndex - 1;
        setHistoryIndex(newIndex);
        setCurrentCommand(commandHistory[commandHistory.length - 1 - newIndex]);
      } else if (historyIndex === 0) {
        setHistoryIndex(-1);
        setCurrentCommand('');
      }
    } else if (e.key === 'Tab') {
      e.preventDefault();
      if (currentCommand.trim()) {
        const filtered = commandSuggestions.filter(cmd =>
          cmd.toLowerCase().startsWith(currentCommand.toLowerCase())
        );
        if (filtered.length > 0) {
          setCommandSuggestions(filtered);
          setShowSuggestions(true);
          setSelectedSuggestion(0);
        }
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setCurrentCommand(value);

    // Show suggestions when typing
    if (value.trim()) {
      const filtered = commandSuggestions.filter(cmd =>
        cmd.toLowerCase().includes(value.toLowerCase())
      );
      if (filtered.length > 0) {
        setCommandSuggestions(filtered);
        setShowSuggestions(true);
        setSelectedSuggestion(0);
      } else {
        setShowSuggestions(false);
      }
    } else {
      setShowSuggestions(false);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getExecutionTime = (block: any) => {
    if (block.endTime) {
      return `${block.endTime - block.startTime}ms`;
    }
    return 'running...';
  };

  if (!activeSession) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3" />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No Terminal Session</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">Create a new terminal session to get started</p>
          <button
            onClick={() => createSession()}
            className="btn btn-primary"
          >
            Create Terminal Session
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Terminal Header */}
      <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {activeSession.name}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {activeSession.workingDirectory}
            </span>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <span>{activeSession.blocks.length} commands</span>
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          </div>
        </div>
      </div>

      {/* Terminal Content */}
      <div 
        ref={terminalRef}
        className="flex-1 bg-black text-green-400 font-mono text-sm p-4 overflow-y-auto custom-scrollbar"
      >
        {/* Welcome Message */}
        <div className="mb-4 text-blue-400">
          <div>NEXUS AI Terminal - Intelligent Development Environment</div>
          <div className="text-gray-500">Type 'ai &lt;question&gt;' or 'nexus &lt;question&gt;' for AI assistance</div>
          <div className="text-gray-500">Session: {activeSession.name}</div>
          <div className="text-gray-500">Working Directory: {activeSession.workingDirectory}</div>
          <div>{'─'.repeat(60)}</div>
        </div>

        {/* Command Blocks */}
        {activeSession.blocks.map((block) => (
          <div key={block.id} className="mb-4">
            {/* Command Header */}
            <div className="flex items-center justify-between mb-1 text-gray-400">
              <div className="flex items-center space-x-2">
                <span className="text-blue-400">Block #{block.id.slice(-4)}</span>
                <span>{formatTimestamp(block.startTime)}</span>
                <span className={`px-2 py-1 rounded text-xs ${
                  block.status === 'completed' ? 'bg-green-900 text-green-300' :
                  block.status === 'failed' ? 'bg-red-900 text-red-300' :
                  block.status === 'running' ? 'bg-yellow-900 text-yellow-300' :
                  'bg-gray-900 text-gray-300'
                }`}>
                  {block.status}
                </span>
                <span>{getExecutionTime(block)}</span>
              </div>
            </div>

            {/* Command Input */}
            <div className="flex items-center mb-2">
              <span className="text-blue-400 mr-2">$</span>
              <span className="text-white">{block.command}</span>
            </div>

            {/* Command Output */}
            {block.output && (
              <div className="ml-4 whitespace-pre-wrap text-green-400 border-l-2 border-gray-700 pl-4">
                {block.output}
              </div>
            )}

            {/* Running Indicator */}
            {block.status === 'running' && (
              <div className="ml-4 flex items-center space-x-2 text-yellow-400">
                <div className="animate-spin w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full"></div>
                <span>Executing...</span>
              </div>
            )}
          </div>
        ))}

        {/* Current Command Input */}
        <div className="relative">
          <form onSubmit={handleCommandSubmit} className="flex items-center">
            <span className="text-blue-400 mr-2">$</span>
            <input
              ref={inputRef}
              type="text"
              value={currentCommand}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className="flex-1 bg-transparent text-white outline-none"
              placeholder="Enter command or 'ai <question>' for AI assistance... (Tab for suggestions)"
              autoComplete="off"
            />
          </form>

          {/* Command Suggestions */}
          {showSuggestions && commandSuggestions.length > 0 && (
            <div className="absolute bottom-full left-0 right-0 mb-2 bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto z-10">
              <div className="p-2 text-xs text-gray-400 border-b border-gray-600">
                Suggestions (↑↓ to navigate, Tab/Enter to select, Esc to close)
              </div>
              {commandSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  className={`px-3 py-2 cursor-pointer text-sm ${
                    index === selectedSuggestion
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-700'
                  }`}
                  onClick={() => {
                    setCurrentCommand(suggestion);
                    setShowSuggestions(false);
                  }}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-blue-400">$</span>
                    <span>{suggestion}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Cursor */}
        <div className="inline-block w-2 h-5 bg-green-400 animate-pulse ml-2"></div>
      </div>

      {/* Terminal Footer */}
      <div className="bg-gray-100 dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center space-x-4">
            <span>Commands: {activeSession.blocks.length}</span>
            <span>Success: {activeSession.blocks.filter(b => b.status === 'completed').length}</span>
            <span>Failed: {activeSession.blocks.filter(b => b.status === 'failed').length}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span>Press ↑/↓ for history</span>
            <span>•</span>
            <span>Type 'ai' for AI help</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default TerminalView;
