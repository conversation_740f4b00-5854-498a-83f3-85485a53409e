import React, { useState, useEffect } from 'react';
import { useFileSystem } from '../contexts/FileSystemContext';
import { useAI } from '../contexts/AIContext';

interface CodeInsight {
  id: string;
  type: 'bug' | 'performance' | 'security' | 'style' | 'suggestion';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  file: string;
  line?: number;
  suggestion: string;
  confidence: number;
}

interface CodeMetrics {
  linesOfCode: number;
  complexity: number;
  maintainability: number;
  testCoverage: number;
  duplicateCode: number;
  technicalDebt: number;
}

function CodeAnalysis() {
  const { selectedFile, openFiles, fileContents } = useFileSystem();
  const { sendMessage } = useAI();
  const [insights, setInsights] = useState<CodeInsight[]>([]);
  const [metrics, setMetrics] = useState<CodeMetrics | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedInsightType, setSelectedInsightType] = useState<string>('all');

  useEffect(() => {
    if (selectedFile && fileContents[selectedFile]) {
      analyzeCode();
    }
  }, [selectedFile, fileContents]);

  const analyzeCode = async () => {
    if (!selectedFile || !fileContents[selectedFile]) return;

    setIsAnalyzing(true);
    try {
      // Simulate code analysis - in a real implementation, this would call the backend
      const mockInsights: CodeInsight[] = [
        {
          id: '1',
          type: 'bug',
          severity: 'high',
          title: 'Potential Null Pointer Exception',
          description: 'Variable may be null before use',
          file: selectedFile,
          line: 42,
          suggestion: 'Add null check before accessing the variable',
          confidence: 0.85
        },
        {
          id: '2',
          type: 'performance',
          severity: 'medium',
          title: 'Inefficient Loop',
          description: 'Nested loop with O(n²) complexity',
          file: selectedFile,
          line: 78,
          suggestion: 'Consider using a Map or Set for faster lookups',
          confidence: 0.75
        },
        {
          id: '3',
          type: 'security',
          severity: 'critical',
          title: 'SQL Injection Vulnerability',
          description: 'User input directly concatenated to SQL query',
          file: selectedFile,
          line: 156,
          suggestion: 'Use parameterized queries or prepared statements',
          confidence: 0.95
        },
        {
          id: '4',
          type: 'style',
          severity: 'low',
          title: 'Inconsistent Naming',
          description: 'Variable name does not follow camelCase convention',
          file: selectedFile,
          line: 23,
          suggestion: 'Rename variable to follow camelCase convention',
          confidence: 0.90
        }
      ];

      const mockMetrics: CodeMetrics = {
        linesOfCode: fileContents[selectedFile].split('\n').length,
        complexity: Math.floor(Math.random() * 20) + 5,
        maintainability: Math.floor(Math.random() * 40) + 60,
        testCoverage: Math.floor(Math.random() * 50) + 30,
        duplicateCode: Math.floor(Math.random() * 15) + 2,
        technicalDebt: Math.floor(Math.random() * 30) + 10
      };

      setInsights(mockInsights);
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Code analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleAIFix = async (insight: CodeInsight) => {
    const message = `Help me fix this ${insight.type} issue: ${insight.title}. ${insight.description}. Suggested fix: ${insight.suggestion}. File: ${insight.file}${insight.line ? `, Line: ${insight.line}` : ''}`;
    
    await sendMessage(message, {
      currentFile: selectedFile,
      openFiles,
      projectType: 'code-analysis',
      recentCommands: [],
      systemState: null,
    });
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'bug':
        return '🐛';
      case 'performance':
        return '⚡';
      case 'security':
        return '🔒';
      case 'style':
        return '🎨';
      case 'suggestion':
        return '💡';
      default:
        return '📝';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'high':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900/20';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  const filteredInsights = selectedInsightType === 'all' 
    ? insights 
    : insights.filter(insight => insight.type === selectedInsightType);

  if (!selectedFile) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Code Analysis</h3>
          <p className="text-gray-600 dark:text-gray-400">
            Select a file to analyze code quality and get intelligent insights
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Code Analysis</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {selectedFile.split('/').pop()}
            </p>
          </div>
          <button
            onClick={analyzeCode}
            disabled={isAnalyzing}
            className="btn btn-primary"
          >
            {isAnalyzing ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Analyzing...</span>
              </div>
            ) : (
              '🔍 Analyze Code'
            )}
          </button>
        </div>
      </div>

      {/* Metrics */}
      {metrics && (
        <div className="bg-gray-50 dark:bg-dark-900 border-b border-gray-200 dark:border-dark-700 p-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{metrics.linesOfCode}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Lines of Code</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">{metrics.complexity}</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Complexity</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">{metrics.maintainability}%</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Maintainability</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{metrics.testCoverage}%</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Test Coverage</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">{metrics.duplicateCode}%</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Duplicate Code</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{metrics.technicalDebt}h</div>
              <div className="text-xs text-gray-600 dark:text-gray-400">Technical Debt</div>
            </div>
          </div>
        </div>
      )}

      {/* Filter Tabs */}
      <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-2">
        <div className="flex space-x-1">
          {['all', 'bug', 'performance', 'security', 'style', 'suggestion'].map((type) => (
            <button
              key={type}
              onClick={() => setSelectedInsightType(type)}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                selectedInsightType === type
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-700'
              }`}
            >
              {getInsightIcon(type)} {type.charAt(0).toUpperCase() + type.slice(1)}
              {type !== 'all' && (
                <span className="ml-1 text-xs">
                  ({insights.filter(i => i.type === type).length})
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Insights List */}
      <div className="flex-1 overflow-y-auto p-4">
        {filteredInsights.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-4xl mb-2">✨</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {selectedInsightType === 'all' ? 'No Issues Found' : `No ${selectedInsightType} Issues`}
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Your code looks great! Keep up the good work.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredInsights.map((insight) => (
              <div
                key={insight.id}
                className="bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-700 rounded-lg p-4"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-lg">{getInsightIcon(insight.type)}</span>
                      <span className="font-medium text-gray-900 dark:text-white">
                        {insight.title}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(insight.severity)}`}>
                        {insight.severity}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {Math.round(insight.confidence * 100)}% confidence
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {insight.description}
                    </p>
                    {insight.line && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                        Line {insight.line}
                      </p>
                    )}
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded p-2">
                      <p className="text-sm text-blue-800 dark:text-blue-200">
                        💡 <strong>Suggestion:</strong> {insight.suggestion}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleAIFix(insight)}
                    className="ml-4 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                  >
                    🤖 AI Fix
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default CodeAnalysis;
