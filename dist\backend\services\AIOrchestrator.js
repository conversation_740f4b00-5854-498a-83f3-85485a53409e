"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIOrchestrator = void 0;
const openai_1 = __importDefault(require("openai"));
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const groq_sdk_1 = __importDefault(require("groq-sdk"));
class AIOrchestrator {
    constructor() {
        this.providers = new Map();
        this.clients = new Map();
        this.requestHistory = [];
        this.responseHistory = [];
        this.initializeProviders();
    }
    initializeProviders() {
        // OpenAI Provider
        if (process.env.OPENAI_API_KEY) {
            const openai = new openai_1.default({
                apiKey: process.env.OPENAI_API_KEY,
            });
            this.clients.set('openai', openai);
            this.providers.set('openai', {
                id: 'openai',
                name: 'OpenAI GPT-4',
                type: 'openai',
                capabilities: [
                    { type: 'code', level: 'expert' },
                    { type: 'reasoning', level: 'expert' },
                    { type: 'multimodal', level: 'advanced' },
                ],
                rateLimits: { requestsPerMin: 60, tokensPerMin: 10000 },
                costPer1kTokens: 0.03,
                responseTime: 2.5,
                specializations: ['general', 'code', 'analysis'],
                isAvailable: true,
            });
        }
        // Anthropic Provider
        if (process.env.ANTHROPIC_API_KEY) {
            const anthropic = new sdk_1.default({
                apiKey: process.env.ANTHROPIC_API_KEY,
            });
            this.clients.set('anthropic', anthropic);
            this.providers.set('anthropic', {
                id: 'anthropic',
                name: 'Anthropic Claude',
                type: 'anthropic',
                capabilities: [
                    { type: 'code', level: 'expert' },
                    { type: 'reasoning', level: 'expert' },
                    { type: 'analysis', level: 'expert' },
                ],
                rateLimits: { requestsPerMin: 50, tokensPerMin: 8000 },
                costPer1kTokens: 0.008,
                responseTime: 1.8,
                specializations: ['code', 'reasoning', 'safety'],
                isAvailable: true,
            });
        }
        // Groq Provider
        if (process.env.GROQ_API_KEY) {
            const groq = new groq_sdk_1.default({
                apiKey: process.env.GROQ_API_KEY,
            });
            this.clients.set('groq', groq);
            this.providers.set('groq', {
                id: 'groq',
                name: 'Groq Llama',
                type: 'groq',
                capabilities: [
                    { type: 'code', level: 'advanced' },
                    { type: 'speed', level: 'expert' },
                ],
                rateLimits: { requestsPerMin: 100, tokensPerMin: 15000 },
                costPer1kTokens: 0.0,
                responseTime: 0.5,
                specializations: ['speed', 'code'],
                isAvailable: true,
            });
        }
        console.log(`🤖 Initialized ${this.providers.size} AI providers`);
    }
    async routeQuery(request) {
        const startTime = Date.now();
        // Store request
        this.requestHistory.push(request);
        // Select best provider for this request
        const provider = this.selectProvider(request);
        if (!provider) {
            throw new Error('No available AI providers');
        }
        try {
            const content = await this.executeQuery(provider.id, request);
            const responseTime = Date.now() - startTime;
            const response = {
                id: this.generateId(),
                requestId: request.id,
                content,
                provider: provider.id,
                confidence: this.calculateConfidence(content, provider),
                suggestions: this.generateSuggestions(content, request.context),
                metadata: {
                    tokensUsed: this.estimateTokens(request.query + content),
                    responseTime,
                    cost: this.calculateCost(provider, content),
                },
                timestamp: Date.now(),
            };
            this.responseHistory.push(response);
            return response;
        }
        catch (error) {
            console.error(`Error with provider ${provider.id}:`, error);
            // Try fallback provider
            const fallbackProvider = this.selectFallbackProvider(provider.id);
            if (fallbackProvider) {
                return this.routeQuery({ ...request, provider: fallbackProvider.id });
            }
            throw error;
        }
    }
    selectProvider(request) {
        const availableProviders = Array.from(this.providers.values())
            .filter(p => p.isAvailable);
        if (availableProviders.length === 0)
            return null;
        // If specific provider requested
        if (request.provider) {
            const provider = this.providers.get(request.provider);
            return provider?.isAvailable ? provider : null;
        }
        // Smart routing based on request characteristics
        if (request.priority === 'urgent' || request.query.includes('fast') || request.query.includes('quick')) {
            // Prioritize speed
            return availableProviders.sort((a, b) => a.responseTime - b.responseTime)[0];
        }
        if (request.query.includes('code') || request.query.includes('function') || request.query.includes('debug')) {
            // Prioritize code capabilities
            return availableProviders
                .filter(p => p.specializations.includes('code'))
                .sort((a, b) => b.capabilities.length - a.capabilities.length)[0];
        }
        // Default: balance cost and performance
        return availableProviders
            .sort((a, b) => (a.costPer1kTokens * a.responseTime) - (b.costPer1kTokens * b.responseTime))[0];
    }
    selectFallbackProvider(excludeId) {
        const availableProviders = Array.from(this.providers.values())
            .filter(p => p.isAvailable && p.id !== excludeId);
        return availableProviders.length > 0 ? availableProviders[0] : null;
    }
    async executeQuery(providerId, request) {
        const client = this.clients.get(providerId);
        const provider = this.providers.get(providerId);
        if (!client || !provider) {
            throw new Error(`Provider ${providerId} not available`);
        }
        const systemPrompt = this.buildSystemPrompt(request.context);
        const userPrompt = this.buildUserPrompt(request.query, request.context);
        switch (provider.type) {
            case 'openai':
                const openaiResponse = await client.chat.completions.create({
                    model: 'gpt-4',
                    messages: [
                        { role: 'system', content: systemPrompt },
                        { role: 'user', content: userPrompt },
                    ],
                    temperature: 0.7,
                    max_tokens: 2000,
                });
                return openaiResponse.choices[0]?.message?.content || '';
            case 'anthropic':
                const anthropicResponse = await client.messages.create({
                    model: 'claude-3-sonnet-20240229',
                    max_tokens: 2000,
                    system: systemPrompt,
                    messages: [{ role: 'user', content: userPrompt }],
                });
                return anthropicResponse.content[0]?.text || '';
            case 'groq':
                const groqResponse = await client.chat.completions.create({
                    model: 'llama2-70b-4096',
                    messages: [
                        { role: 'system', content: systemPrompt },
                        { role: 'user', content: userPrompt },
                    ],
                    temperature: 0.7,
                    max_tokens: 2000,
                });
                return groqResponse.choices[0]?.message?.content || '';
            default:
                throw new Error(`Unsupported provider type: ${provider.type}`);
        }
    }
    buildSystemPrompt(context) {
        return `You are NEXUS AI, an intelligent development assistant with JARVIS-like capabilities.

Context:
- Current file: ${context.currentFile || 'None'}
- Open files: ${context.openFiles.join(', ') || 'None'}
- Project type: ${context.projectType}
- Recent commands: ${context.recentCommands.slice(-3).join(', ') || 'None'}
- System CPU: ${context.systemState.cpu.usage}%
- System Memory: ${context.systemState.memory.percentage}%

You should:
1. Provide intelligent, context-aware responses
2. Suggest optimizations and improvements
3. Anticipate user needs based on patterns
4. Offer actionable solutions
5. Be concise but comprehensive

Respond in a helpful, professional tone with practical suggestions.`;
    }
    buildUserPrompt(query, context) {
        let prompt = query;
        if (context.userIntent) {
            prompt = `User Intent: ${context.userIntent}\n\nQuery: ${query}`;
        }
        return prompt;
    }
    calculateConfidence(content, provider) {
        // Simple confidence calculation based on response length and provider capabilities
        const baseConfidence = Math.min(content.length / 100, 1) * 0.5;
        const providerBonus = provider.capabilities.length * 0.1;
        return Math.min(baseConfidence + providerBonus, 1);
    }
    generateSuggestions(content, context) {
        const suggestions = [];
        if (content.includes('error') || content.includes('fix')) {
            suggestions.push('Run diagnostics', 'Check logs', 'Test solution');
        }
        if (content.includes('optimize') || content.includes('performance')) {
            suggestions.push('Profile code', 'Analyze bundle', 'Check metrics');
        }
        if (content.includes('test') || content.includes('testing')) {
            suggestions.push('Run tests', 'Generate test cases', 'Check coverage');
        }
        return suggestions;
    }
    estimateTokens(text) {
        // Rough estimation: ~4 characters per token
        return Math.ceil(text.length / 4);
    }
    calculateCost(provider, content) {
        const tokens = this.estimateTokens(content);
        return (tokens / 1000) * provider.costPer1kTokens;
    }
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    }
    getAvailableProviders() {
        return Array.from(this.providers.values()).filter(p => p.isAvailable);
    }
    getProvider(id) {
        return this.providers.get(id);
    }
    getRequestHistory() {
        return this.requestHistory.slice(-100); // Last 100 requests
    }
    getResponseHistory() {
        return this.responseHistory.slice(-100); // Last 100 responses
    }
}
exports.AIOrchestrator = AIOrchestrator;
