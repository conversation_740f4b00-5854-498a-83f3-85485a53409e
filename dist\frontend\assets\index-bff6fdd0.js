(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&r(l)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function Vd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var eu={exports:{}},Is={},tu={exports:{}},U={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jr=Symbol.for("react.element"),Wd=Symbol.for("react.portal"),Hd=Symbol.for("react.fragment"),Qd=Symbol.for("react.strict_mode"),qd=Symbol.for("react.profiler"),Kd=Symbol.for("react.provider"),Yd=Symbol.for("react.context"),Xd=Symbol.for("react.forward_ref"),Gd=Symbol.for("react.suspense"),Jd=Symbol.for("react.memo"),Zd=Symbol.for("react.lazy"),Rl=Symbol.iterator;function ef(e){return e===null||typeof e!="object"?null:(e=Rl&&e[Rl]||e["@@iterator"],typeof e=="function"?e:null)}var nu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ru=Object.assign,su={};function En(e,t,n){this.props=e,this.context=t,this.refs=su,this.updater=n||nu}En.prototype.isReactComponent={};En.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};En.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function iu(){}iu.prototype=En.prototype;function Oo(e,t,n){this.props=e,this.context=t,this.refs=su,this.updater=n||nu}var Ao=Oo.prototype=new iu;Ao.constructor=Oo;ru(Ao,En.prototype);Ao.isPureReactComponent=!0;var Ol=Array.isArray,ou=Object.prototype.hasOwnProperty,zo={current:null},lu={key:!0,ref:!0,__self:!0,__source:!0};function au(e,t,n){var r,s={},i=null,l=null;if(t!=null)for(r in t.ref!==void 0&&(l=t.ref),t.key!==void 0&&(i=""+t.key),t)ou.call(t,r)&&!lu.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];s.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:jr,type:e,key:i,ref:l,props:s,_owner:zo.current}}function tf(e,t){return{$$typeof:jr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Io(e){return typeof e=="object"&&e!==null&&e.$$typeof===jr}function nf(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Al=/\/+/g;function ni(e,t){return typeof e=="object"&&e!==null&&e.key!=null?nf(""+e.key):t.toString(36)}function Xr(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var l=!1;if(e===null)l=!0;else switch(i){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case jr:case Wd:l=!0}}if(l)return l=e,s=s(l),e=r===""?"."+ni(l,0):r,Ol(s)?(n="",e!=null&&(n=e.replace(Al,"$&/")+"/"),Xr(s,t,n,"",function(c){return c})):s!=null&&(Io(s)&&(s=tf(s,n+(!s.key||l&&l.key===s.key?"":(""+s.key).replace(Al,"$&/")+"/")+e)),t.push(s)),1;if(l=0,r=r===""?".":r+":",Ol(e))for(var a=0;a<e.length;a++){i=e[a];var u=r+ni(i,a);l+=Xr(i,t,n,u,s)}else if(u=ef(e),typeof u=="function")for(e=u.call(e),a=0;!(i=e.next()).done;)i=i.value,u=r+ni(i,a++),l+=Xr(i,t,n,u,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function Lr(e,t,n){if(e==null)return e;var r=[],s=0;return Xr(e,r,"","",function(i){return t.call(n,i,s++)}),r}function rf(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ve={current:null},Gr={transition:null},sf={ReactCurrentDispatcher:ve,ReactCurrentBatchConfig:Gr,ReactCurrentOwner:zo};function uu(){throw Error("act(...) is not supported in production builds of React.")}U.Children={map:Lr,forEach:function(e,t,n){Lr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Lr(e,function(){t++}),t},toArray:function(e){return Lr(e,function(t){return t})||[]},only:function(e){if(!Io(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};U.Component=En;U.Fragment=Hd;U.Profiler=qd;U.PureComponent=Oo;U.StrictMode=Qd;U.Suspense=Gd;U.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=sf;U.act=uu;U.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ru({},e.props),s=e.key,i=e.ref,l=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,l=zo.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)ou.call(t,u)&&!lu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:jr,type:e.type,key:s,ref:i,props:r,_owner:l}};U.createContext=function(e){return e={$$typeof:Yd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Kd,_context:e},e.Consumer=e};U.createElement=au;U.createFactory=function(e){var t=au.bind(null,e);return t.type=e,t};U.createRef=function(){return{current:null}};U.forwardRef=function(e){return{$$typeof:Xd,render:e}};U.isValidElement=Io;U.lazy=function(e){return{$$typeof:Zd,_payload:{_status:-1,_result:e},_init:rf}};U.memo=function(e,t){return{$$typeof:Jd,type:e,compare:t===void 0?null:t}};U.startTransition=function(e){var t=Gr.transition;Gr.transition={};try{e()}finally{Gr.transition=t}};U.unstable_act=uu;U.useCallback=function(e,t){return ve.current.useCallback(e,t)};U.useContext=function(e){return ve.current.useContext(e)};U.useDebugValue=function(){};U.useDeferredValue=function(e){return ve.current.useDeferredValue(e)};U.useEffect=function(e,t){return ve.current.useEffect(e,t)};U.useId=function(){return ve.current.useId()};U.useImperativeHandle=function(e,t,n){return ve.current.useImperativeHandle(e,t,n)};U.useInsertionEffect=function(e,t){return ve.current.useInsertionEffect(e,t)};U.useLayoutEffect=function(e,t){return ve.current.useLayoutEffect(e,t)};U.useMemo=function(e,t){return ve.current.useMemo(e,t)};U.useReducer=function(e,t,n){return ve.current.useReducer(e,t,n)};U.useRef=function(e){return ve.current.useRef(e)};U.useState=function(e){return ve.current.useState(e)};U.useSyncExternalStore=function(e,t,n){return ve.current.useSyncExternalStore(e,t,n)};U.useTransition=function(){return ve.current.useTransition()};U.version="18.3.1";tu.exports=U;var k=tu.exports;const wt=Vd(k);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var of=k,lf=Symbol.for("react.element"),af=Symbol.for("react.fragment"),uf=Object.prototype.hasOwnProperty,cf=of.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,df={key:!0,ref:!0,__self:!0,__source:!0};function cu(e,t,n){var r,s={},i=null,l=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(l=t.ref);for(r in t)uf.call(t,r)&&!df.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:lf,type:e,key:i,ref:l,props:s,_owner:cf.current}}Is.Fragment=af;Is.jsx=cu;Is.jsxs=cu;eu.exports=Is;var o=eu.exports,Oi={},du={exports:{}},Pe={},fu={exports:{}},hu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,I){var D=P.length;P.push(I);e:for(;0<D;){var M=D-1>>>1,F=P[M];if(0<s(F,I))P[M]=I,P[D]=F,D=M;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var I=P[0],D=P.pop();if(D!==I){P[0]=D;e:for(var M=0,F=P.length,ee=F>>>1;M<ee;){var Ce=2*(M+1)-1,Ke=P[Ce],It=Ce+1,br=P[It];if(0>s(Ke,D))It<F&&0>s(br,Ke)?(P[M]=br,P[It]=D,M=It):(P[M]=Ke,P[Ce]=D,M=Ce);else if(It<F&&0>s(br,D))P[M]=br,P[It]=D,M=It;else break e}}return I}function s(P,I){var D=P.sortIndex-I.sortIndex;return D!==0?D:P.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var l=Date,a=l.now();e.unstable_now=function(){return l.now()-a}}var u=[],c=[],p=1,m=null,g=3,j=!1,x=!1,_=!1,R=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(P){for(var I=n(c);I!==null;){if(I.callback===null)r(c);else if(I.startTime<=P)r(c),I.sortIndex=I.expirationTime,t(u,I);else break;I=n(c)}}function w(P){if(_=!1,h(P),!x)if(n(u)!==null)x=!0,A(N);else{var I=n(c);I!==null&&$(w,I.startTime-P)}}function N(P,I){x=!1,_&&(_=!1,f(y),y=-1),j=!0;var D=g;try{for(h(I),m=n(u);m!==null&&(!(m.expirationTime>I)||P&&!S());){var M=m.callback;if(typeof M=="function"){m.callback=null,g=m.priorityLevel;var F=M(m.expirationTime<=I);I=e.unstable_now(),typeof F=="function"?m.callback=F:m===n(u)&&r(u),h(I)}else r(u);m=n(u)}if(m!==null)var ee=!0;else{var Ce=n(c);Ce!==null&&$(w,Ce.startTime-I),ee=!1}return ee}finally{m=null,g=D,j=!1}}var b=!1,v=null,y=-1,C=5,E=-1;function S(){return!(e.unstable_now()-E<C)}function O(){if(v!==null){var P=e.unstable_now();E=P;var I=!0;try{I=v(!0,P)}finally{I?B():(b=!1,v=null)}}else b=!1}var B;if(typeof d=="function")B=function(){d(O)};else if(typeof MessageChannel<"u"){var X=new MessageChannel,T=X.port2;X.port1.onmessage=O,B=function(){T.postMessage(null)}}else B=function(){R(O,0)};function A(P){v=P,b||(b=!0,B())}function $(P,I){y=R(function(){P(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){x||j||(x=!0,A(N))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(P){switch(g){case 1:case 2:case 3:var I=3;break;default:I=g}var D=g;g=I;try{return P()}finally{g=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,I){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var D=g;g=P;try{return I()}finally{g=D}},e.unstable_scheduleCallback=function(P,I,D){var M=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?M+D:M):D=M,P){case 1:var F=-1;break;case 2:F=250;break;case 5:F=**********;break;case 4:F=1e4;break;default:F=5e3}return F=D+F,P={id:p++,callback:I,priorityLevel:P,startTime:D,expirationTime:F,sortIndex:-1},D>M?(P.sortIndex=D,t(c,P),n(u)===null&&P===n(c)&&(_?(f(y),y=-1):_=!0,$(w,D-M))):(P.sortIndex=F,t(u,P),x||j||(x=!0,A(N))),P},e.unstable_shouldYield=S,e.unstable_wrapCallback=function(P){var I=g;return function(){var D=g;g=I;try{return P.apply(this,arguments)}finally{g=D}}}})(hu);fu.exports=hu;var ff=fu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hf=k,Te=ff;function L(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var pu=new Set,sr={};function Xt(e,t){xn(e,t),xn(e+"Capture",t)}function xn(e,t){for(sr[e]=t,e=0;e<t.length;e++)pu.add(t[e])}var lt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ai=Object.prototype.hasOwnProperty,pf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,zl={},Il={};function mf(e){return Ai.call(Il,e)?!0:Ai.call(zl,e)?!1:pf.test(e)?Il[e]=!0:(zl[e]=!0,!1)}function gf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function yf(e,t,n,r){if(t===null||typeof t>"u"||gf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function xe(e,t,n,r,s,i,l){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var de={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){de[e]=new xe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];de[t]=new xe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){de[e]=new xe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){de[e]=new xe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){de[e]=new xe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){de[e]=new xe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){de[e]=new xe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){de[e]=new xe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){de[e]=new xe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Fo=/[\-:]([a-z])/g;function Do(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Fo,Do);de[t]=new xe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Fo,Do);de[t]=new xe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Fo,Do);de[t]=new xe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){de[e]=new xe(e,1,!1,e.toLowerCase(),null,!1,!1)});de.xlinkHref=new xe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){de[e]=new xe(e,1,!1,e.toLowerCase(),null,!0,!0)});function Bo(e,t,n,r){var s=de.hasOwnProperty(t)?de[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(yf(t,n,s,r)&&(n=null),r||s===null?mf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var dt=hf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Tr=Symbol.for("react.element"),Zt=Symbol.for("react.portal"),en=Symbol.for("react.fragment"),$o=Symbol.for("react.strict_mode"),zi=Symbol.for("react.profiler"),mu=Symbol.for("react.provider"),gu=Symbol.for("react.context"),Uo=Symbol.for("react.forward_ref"),Ii=Symbol.for("react.suspense"),Fi=Symbol.for("react.suspense_list"),Vo=Symbol.for("react.memo"),pt=Symbol.for("react.lazy"),yu=Symbol.for("react.offscreen"),Fl=Symbol.iterator;function Rn(e){return e===null||typeof e!="object"?null:(e=Fl&&e[Fl]||e["@@iterator"],typeof e=="function"?e:null)}var Z=Object.assign,ri;function Un(e){if(ri===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ri=t&&t[1]||""}return`
`+ri+e}var si=!1;function ii(e,t){if(!e||si)return"";si=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),i=r.stack.split(`
`),l=s.length-1,a=i.length-1;1<=l&&0<=a&&s[l]!==i[a];)a--;for(;1<=l&&0<=a;l--,a--)if(s[l]!==i[a]){if(l!==1||a!==1)do if(l--,a--,0>a||s[l]!==i[a]){var u=`
`+s[l].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=l&&0<=a);break}}}finally{si=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Un(e):""}function vf(e){switch(e.tag){case 5:return Un(e.type);case 16:return Un("Lazy");case 13:return Un("Suspense");case 19:return Un("SuspenseList");case 0:case 2:case 15:return e=ii(e.type,!1),e;case 11:return e=ii(e.type.render,!1),e;case 1:return e=ii(e.type,!0),e;default:return""}}function Di(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case en:return"Fragment";case Zt:return"Portal";case zi:return"Profiler";case $o:return"StrictMode";case Ii:return"Suspense";case Fi:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case gu:return(e.displayName||"Context")+".Consumer";case mu:return(e._context.displayName||"Context")+".Provider";case Uo:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Vo:return t=e.displayName||null,t!==null?t:Di(e.type)||"Memo";case pt:t=e._payload,e=e._init;try{return Di(e(t))}catch{}}return null}function xf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Di(t);case 8:return t===$o?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Pt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function vu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function wf(e){var t=vu(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(l){r=""+l,i.call(this,l)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(l){r=""+l},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Pr(e){e._valueTracker||(e._valueTracker=wf(e))}function xu(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=vu(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function fs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Bi(e,t){var n=t.checked;return Z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Dl(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Pt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function wu(e,t){t=t.checked,t!=null&&Bo(e,"checked",t,!1)}function $i(e,t){wu(e,t);var n=Pt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ui(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ui(e,t.type,Pt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Bl(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ui(e,t,n){(t!=="number"||fs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Vn=Array.isArray;function hn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Pt(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Vi(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(L(91));return Z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function $l(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(L(92));if(Vn(n)){if(1<n.length)throw Error(L(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Pt(n)}}function ku(e,t){var n=Pt(t.value),r=Pt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ul(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ju(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Wi(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ju(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Mr,Nu=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Mr=Mr||document.createElement("div"),Mr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Mr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ir(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Kn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},kf=["Webkit","ms","Moz","O"];Object.keys(Kn).forEach(function(e){kf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Kn[t]=Kn[e]})});function Su(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Kn.hasOwnProperty(e)&&Kn[e]?(""+t).trim():t+"px"}function Cu(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Su(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var jf=Z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Hi(e,t){if(t){if(jf[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(L(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(L(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(L(61))}if(t.style!=null&&typeof t.style!="object")throw Error(L(62))}}function Qi(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var qi=null;function Wo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ki=null,pn=null,mn=null;function Vl(e){if(e=Cr(e)){if(typeof Ki!="function")throw Error(L(280));var t=e.stateNode;t&&(t=Us(t),Ki(e.stateNode,e.type,t))}}function Eu(e){pn?mn?mn.push(e):mn=[e]:pn=e}function _u(){if(pn){var e=pn,t=mn;if(mn=pn=null,Vl(e),t)for(e=0;e<t.length;e++)Vl(t[e])}}function bu(e,t){return e(t)}function Lu(){}var oi=!1;function Tu(e,t,n){if(oi)return e(t,n);oi=!0;try{return bu(e,t,n)}finally{oi=!1,(pn!==null||mn!==null)&&(Lu(),_u())}}function or(e,t){var n=e.stateNode;if(n===null)return null;var r=Us(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(L(231,t,typeof n));return n}var Yi=!1;if(lt)try{var On={};Object.defineProperty(On,"passive",{get:function(){Yi=!0}}),window.addEventListener("test",On,On),window.removeEventListener("test",On,On)}catch{Yi=!1}function Nf(e,t,n,r,s,i,l,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(p){this.onError(p)}}var Yn=!1,hs=null,ps=!1,Xi=null,Sf={onError:function(e){Yn=!0,hs=e}};function Cf(e,t,n,r,s,i,l,a,u){Yn=!1,hs=null,Nf.apply(Sf,arguments)}function Ef(e,t,n,r,s,i,l,a,u){if(Cf.apply(this,arguments),Yn){if(Yn){var c=hs;Yn=!1,hs=null}else throw Error(L(198));ps||(ps=!0,Xi=c)}}function Gt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Pu(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Wl(e){if(Gt(e)!==e)throw Error(L(188))}function _f(e){var t=e.alternate;if(!t){if(t=Gt(e),t===null)throw Error(L(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return Wl(s),e;if(i===r)return Wl(s),t;i=i.sibling}throw Error(L(188))}if(n.return!==r.return)n=s,r=i;else{for(var l=!1,a=s.child;a;){if(a===n){l=!0,n=s,r=i;break}if(a===r){l=!0,r=s,n=i;break}a=a.sibling}if(!l){for(a=i.child;a;){if(a===n){l=!0,n=i,r=s;break}if(a===r){l=!0,r=i,n=s;break}a=a.sibling}if(!l)throw Error(L(189))}}if(n.alternate!==r)throw Error(L(190))}if(n.tag!==3)throw Error(L(188));return n.stateNode.current===n?e:t}function Mu(e){return e=_f(e),e!==null?Ru(e):null}function Ru(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ru(e);if(t!==null)return t;e=e.sibling}return null}var Ou=Te.unstable_scheduleCallback,Hl=Te.unstable_cancelCallback,bf=Te.unstable_shouldYield,Lf=Te.unstable_requestPaint,ne=Te.unstable_now,Tf=Te.unstable_getCurrentPriorityLevel,Ho=Te.unstable_ImmediatePriority,Au=Te.unstable_UserBlockingPriority,ms=Te.unstable_NormalPriority,Pf=Te.unstable_LowPriority,zu=Te.unstable_IdlePriority,Fs=null,Je=null;function Mf(e){if(Je&&typeof Je.onCommitFiberRoot=="function")try{Je.onCommitFiberRoot(Fs,e,void 0,(e.current.flags&128)===128)}catch{}}var He=Math.clz32?Math.clz32:Af,Rf=Math.log,Of=Math.LN2;function Af(e){return e>>>=0,e===0?32:31-(Rf(e)/Of|0)|0}var Rr=64,Or=4194304;function Wn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function gs(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,l=n&268435455;if(l!==0){var a=l&~s;a!==0?r=Wn(a):(i&=l,i!==0&&(r=Wn(i)))}else l=n&~s,l!==0?r=Wn(l):i!==0&&(r=Wn(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-He(t),s=1<<n,r|=e[n],t&=~s;return r}function zf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function If(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-He(i),a=1<<l,u=s[l];u===-1?(!(a&n)||a&r)&&(s[l]=zf(a,t)):u<=t&&(e.expiredLanes|=a),i&=~a}}function Gi(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Iu(){var e=Rr;return Rr<<=1,!(Rr&4194240)&&(Rr=64),e}function li(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Nr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-He(t),e[t]=n}function Ff(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-He(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function Qo(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-He(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var H=0;function Fu(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Du,qo,Bu,$u,Uu,Ji=!1,Ar=[],kt=null,jt=null,Nt=null,lr=new Map,ar=new Map,gt=[],Df="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ql(e,t){switch(e){case"focusin":case"focusout":kt=null;break;case"dragenter":case"dragleave":jt=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":lr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ar.delete(t.pointerId)}}function An(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=Cr(t),t!==null&&qo(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Bf(e,t,n,r,s){switch(t){case"focusin":return kt=An(kt,e,t,n,r,s),!0;case"dragenter":return jt=An(jt,e,t,n,r,s),!0;case"mouseover":return Nt=An(Nt,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return lr.set(i,An(lr.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,ar.set(i,An(ar.get(i)||null,e,t,n,r,s)),!0}return!1}function Vu(e){var t=Bt(e.target);if(t!==null){var n=Gt(t);if(n!==null){if(t=n.tag,t===13){if(t=Pu(n),t!==null){e.blockedOn=t,Uu(e.priority,function(){Bu(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Jr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Zi(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);qi=r,n.target.dispatchEvent(r),qi=null}else return t=Cr(n),t!==null&&qo(t),e.blockedOn=n,!1;t.shift()}return!0}function ql(e,t,n){Jr(e)&&n.delete(t)}function $f(){Ji=!1,kt!==null&&Jr(kt)&&(kt=null),jt!==null&&Jr(jt)&&(jt=null),Nt!==null&&Jr(Nt)&&(Nt=null),lr.forEach(ql),ar.forEach(ql)}function zn(e,t){e.blockedOn===t&&(e.blockedOn=null,Ji||(Ji=!0,Te.unstable_scheduleCallback(Te.unstable_NormalPriority,$f)))}function ur(e){function t(s){return zn(s,e)}if(0<Ar.length){zn(Ar[0],e);for(var n=1;n<Ar.length;n++){var r=Ar[n];r.blockedOn===e&&(r.blockedOn=null)}}for(kt!==null&&zn(kt,e),jt!==null&&zn(jt,e),Nt!==null&&zn(Nt,e),lr.forEach(t),ar.forEach(t),n=0;n<gt.length;n++)r=gt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<gt.length&&(n=gt[0],n.blockedOn===null);)Vu(n),n.blockedOn===null&&gt.shift()}var gn=dt.ReactCurrentBatchConfig,ys=!0;function Uf(e,t,n,r){var s=H,i=gn.transition;gn.transition=null;try{H=1,Ko(e,t,n,r)}finally{H=s,gn.transition=i}}function Vf(e,t,n,r){var s=H,i=gn.transition;gn.transition=null;try{H=4,Ko(e,t,n,r)}finally{H=s,gn.transition=i}}function Ko(e,t,n,r){if(ys){var s=Zi(e,t,n,r);if(s===null)yi(e,t,r,vs,n),Ql(e,r);else if(Bf(s,e,t,n,r))r.stopPropagation();else if(Ql(e,r),t&4&&-1<Df.indexOf(e)){for(;s!==null;){var i=Cr(s);if(i!==null&&Du(i),i=Zi(e,t,n,r),i===null&&yi(e,t,r,vs,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else yi(e,t,r,null,n)}}var vs=null;function Zi(e,t,n,r){if(vs=null,e=Wo(r),e=Bt(e),e!==null)if(t=Gt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Pu(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return vs=e,null}function Wu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Tf()){case Ho:return 1;case Au:return 4;case ms:case Pf:return 16;case zu:return 536870912;default:return 16}default:return 16}}var vt=null,Yo=null,Zr=null;function Hu(){if(Zr)return Zr;var e,t=Yo,n=t.length,r,s="value"in vt?vt.value:vt.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var l=n-e;for(r=1;r<=l&&t[n-r]===s[i-r];r++);return Zr=s.slice(e,1<r?1-r:void 0)}function es(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function zr(){return!0}function Kl(){return!1}function Me(e){function t(n,r,s,i,l){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=l,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?zr:Kl,this.isPropagationStopped=Kl,this}return Z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=zr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=zr)},persist:function(){},isPersistent:zr}),t}var _n={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xo=Me(_n),Sr=Z({},_n,{view:0,detail:0}),Wf=Me(Sr),ai,ui,In,Ds=Z({},Sr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Go,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==In&&(In&&e.type==="mousemove"?(ai=e.screenX-In.screenX,ui=e.screenY-In.screenY):ui=ai=0,In=e),ai)},movementY:function(e){return"movementY"in e?e.movementY:ui}}),Yl=Me(Ds),Hf=Z({},Ds,{dataTransfer:0}),Qf=Me(Hf),qf=Z({},Sr,{relatedTarget:0}),ci=Me(qf),Kf=Z({},_n,{animationName:0,elapsedTime:0,pseudoElement:0}),Yf=Me(Kf),Xf=Z({},_n,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Gf=Me(Xf),Jf=Z({},_n,{data:0}),Xl=Me(Jf),Zf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},eh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},th={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function nh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=th[e])?!!t[e]:!1}function Go(){return nh}var rh=Z({},Sr,{key:function(e){if(e.key){var t=Zf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=es(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?eh[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Go,charCode:function(e){return e.type==="keypress"?es(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?es(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),sh=Me(rh),ih=Z({},Ds,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Gl=Me(ih),oh=Z({},Sr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Go}),lh=Me(oh),ah=Z({},_n,{propertyName:0,elapsedTime:0,pseudoElement:0}),uh=Me(ah),ch=Z({},Ds,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),dh=Me(ch),fh=[9,13,27,32],Jo=lt&&"CompositionEvent"in window,Xn=null;lt&&"documentMode"in document&&(Xn=document.documentMode);var hh=lt&&"TextEvent"in window&&!Xn,Qu=lt&&(!Jo||Xn&&8<Xn&&11>=Xn),Jl=String.fromCharCode(32),Zl=!1;function qu(e,t){switch(e){case"keyup":return fh.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ku(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var tn=!1;function ph(e,t){switch(e){case"compositionend":return Ku(t);case"keypress":return t.which!==32?null:(Zl=!0,Jl);case"textInput":return e=t.data,e===Jl&&Zl?null:e;default:return null}}function mh(e,t){if(tn)return e==="compositionend"||!Jo&&qu(e,t)?(e=Hu(),Zr=Yo=vt=null,tn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Qu&&t.locale!=="ko"?null:t.data;default:return null}}var gh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ea(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!gh[e.type]:t==="textarea"}function Yu(e,t,n,r){Eu(r),t=xs(t,"onChange"),0<t.length&&(n=new Xo("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Gn=null,cr=null;function yh(e){oc(e,0)}function Bs(e){var t=sn(e);if(xu(t))return e}function vh(e,t){if(e==="change")return t}var Xu=!1;if(lt){var di;if(lt){var fi="oninput"in document;if(!fi){var ta=document.createElement("div");ta.setAttribute("oninput","return;"),fi=typeof ta.oninput=="function"}di=fi}else di=!1;Xu=di&&(!document.documentMode||9<document.documentMode)}function na(){Gn&&(Gn.detachEvent("onpropertychange",Gu),cr=Gn=null)}function Gu(e){if(e.propertyName==="value"&&Bs(cr)){var t=[];Yu(t,cr,e,Wo(e)),Tu(yh,t)}}function xh(e,t,n){e==="focusin"?(na(),Gn=t,cr=n,Gn.attachEvent("onpropertychange",Gu)):e==="focusout"&&na()}function wh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Bs(cr)}function kh(e,t){if(e==="click")return Bs(t)}function jh(e,t){if(e==="input"||e==="change")return Bs(t)}function Nh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qe=typeof Object.is=="function"?Object.is:Nh;function dr(e,t){if(qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Ai.call(t,s)||!qe(e[s],t[s]))return!1}return!0}function ra(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function sa(e,t){var n=ra(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ra(n)}}function Ju(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ju(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Zu(){for(var e=window,t=fs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=fs(e.document)}return t}function Zo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Sh(e){var t=Zu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ju(n.ownerDocument.documentElement,n)){if(r!==null&&Zo(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=sa(n,i);var l=sa(n,r);s&&l&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ch=lt&&"documentMode"in document&&11>=document.documentMode,nn=null,eo=null,Jn=null,to=!1;function ia(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;to||nn==null||nn!==fs(r)||(r=nn,"selectionStart"in r&&Zo(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Jn&&dr(Jn,r)||(Jn=r,r=xs(eo,"onSelect"),0<r.length&&(t=new Xo("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=nn)))}function Ir(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var rn={animationend:Ir("Animation","AnimationEnd"),animationiteration:Ir("Animation","AnimationIteration"),animationstart:Ir("Animation","AnimationStart"),transitionend:Ir("Transition","TransitionEnd")},hi={},ec={};lt&&(ec=document.createElement("div").style,"AnimationEvent"in window||(delete rn.animationend.animation,delete rn.animationiteration.animation,delete rn.animationstart.animation),"TransitionEvent"in window||delete rn.transitionend.transition);function $s(e){if(hi[e])return hi[e];if(!rn[e])return e;var t=rn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ec)return hi[e]=t[n];return e}var tc=$s("animationend"),nc=$s("animationiteration"),rc=$s("animationstart"),sc=$s("transitionend"),ic=new Map,oa="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rt(e,t){ic.set(e,t),Xt(t,[e])}for(var pi=0;pi<oa.length;pi++){var mi=oa[pi],Eh=mi.toLowerCase(),_h=mi[0].toUpperCase()+mi.slice(1);Rt(Eh,"on"+_h)}Rt(tc,"onAnimationEnd");Rt(nc,"onAnimationIteration");Rt(rc,"onAnimationStart");Rt("dblclick","onDoubleClick");Rt("focusin","onFocus");Rt("focusout","onBlur");Rt(sc,"onTransitionEnd");xn("onMouseEnter",["mouseout","mouseover"]);xn("onMouseLeave",["mouseout","mouseover"]);xn("onPointerEnter",["pointerout","pointerover"]);xn("onPointerLeave",["pointerout","pointerover"]);Xt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Xt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Xt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Xt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),bh=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hn));function la(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Ef(r,t,void 0,e),e.currentTarget=null}function oc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var l=r.length-1;0<=l;l--){var a=r[l],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==i&&s.isPropagationStopped())break e;la(s,a,c),i=u}else for(l=0;l<r.length;l++){if(a=r[l],u=a.instance,c=a.currentTarget,a=a.listener,u!==i&&s.isPropagationStopped())break e;la(s,a,c),i=u}}}if(ps)throw e=Xi,ps=!1,Xi=null,e}function q(e,t){var n=t[oo];n===void 0&&(n=t[oo]=new Set);var r=e+"__bubble";n.has(r)||(lc(t,e,2,!1),n.add(r))}function gi(e,t,n){var r=0;t&&(r|=4),lc(n,e,r,t)}var Fr="_reactListening"+Math.random().toString(36).slice(2);function fr(e){if(!e[Fr]){e[Fr]=!0,pu.forEach(function(n){n!=="selectionchange"&&(bh.has(n)||gi(n,!1,e),gi(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Fr]||(t[Fr]=!0,gi("selectionchange",!1,t))}}function lc(e,t,n,r){switch(Wu(t)){case 1:var s=Uf;break;case 4:s=Vf;break;default:s=Ko}n=s.bind(null,t,n,e),s=void 0,!Yi||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function yi(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var l=r.tag;if(l===3||l===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(l===4)for(l=r.return;l!==null;){var u=l.tag;if((u===3||u===4)&&(u=l.stateNode.containerInfo,u===s||u.nodeType===8&&u.parentNode===s))return;l=l.return}for(;a!==null;){if(l=Bt(a),l===null)return;if(u=l.tag,u===5||u===6){r=i=l;continue e}a=a.parentNode}}r=r.return}Tu(function(){var c=i,p=Wo(n),m=[];e:{var g=ic.get(e);if(g!==void 0){var j=Xo,x=e;switch(e){case"keypress":if(es(n)===0)break e;case"keydown":case"keyup":j=sh;break;case"focusin":x="focus",j=ci;break;case"focusout":x="blur",j=ci;break;case"beforeblur":case"afterblur":j=ci;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=Yl;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=Qf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=lh;break;case tc:case nc:case rc:j=Yf;break;case sc:j=uh;break;case"scroll":j=Wf;break;case"wheel":j=dh;break;case"copy":case"cut":case"paste":j=Gf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=Gl}var _=(t&4)!==0,R=!_&&e==="scroll",f=_?g!==null?g+"Capture":null:g;_=[];for(var d=c,h;d!==null;){h=d;var w=h.stateNode;if(h.tag===5&&w!==null&&(h=w,f!==null&&(w=or(d,f),w!=null&&_.push(hr(d,w,h)))),R)break;d=d.return}0<_.length&&(g=new j(g,x,null,n,p),m.push({event:g,listeners:_}))}}if(!(t&7)){e:{if(g=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",g&&n!==qi&&(x=n.relatedTarget||n.fromElement)&&(Bt(x)||x[at]))break e;if((j||g)&&(g=p.window===p?p:(g=p.ownerDocument)?g.defaultView||g.parentWindow:window,j?(x=n.relatedTarget||n.toElement,j=c,x=x?Bt(x):null,x!==null&&(R=Gt(x),x!==R||x.tag!==5&&x.tag!==6)&&(x=null)):(j=null,x=c),j!==x)){if(_=Yl,w="onMouseLeave",f="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(_=Gl,w="onPointerLeave",f="onPointerEnter",d="pointer"),R=j==null?g:sn(j),h=x==null?g:sn(x),g=new _(w,d+"leave",j,n,p),g.target=R,g.relatedTarget=h,w=null,Bt(p)===c&&(_=new _(f,d+"enter",x,n,p),_.target=h,_.relatedTarget=R,w=_),R=w,j&&x)t:{for(_=j,f=x,d=0,h=_;h;h=Jt(h))d++;for(h=0,w=f;w;w=Jt(w))h++;for(;0<d-h;)_=Jt(_),d--;for(;0<h-d;)f=Jt(f),h--;for(;d--;){if(_===f||f!==null&&_===f.alternate)break t;_=Jt(_),f=Jt(f)}_=null}else _=null;j!==null&&aa(m,g,j,_,!1),x!==null&&R!==null&&aa(m,R,x,_,!0)}}e:{if(g=c?sn(c):window,j=g.nodeName&&g.nodeName.toLowerCase(),j==="select"||j==="input"&&g.type==="file")var N=vh;else if(ea(g))if(Xu)N=jh;else{N=wh;var b=xh}else(j=g.nodeName)&&j.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(N=kh);if(N&&(N=N(e,c))){Yu(m,N,n,p);break e}b&&b(e,g,c),e==="focusout"&&(b=g._wrapperState)&&b.controlled&&g.type==="number"&&Ui(g,"number",g.value)}switch(b=c?sn(c):window,e){case"focusin":(ea(b)||b.contentEditable==="true")&&(nn=b,eo=c,Jn=null);break;case"focusout":Jn=eo=nn=null;break;case"mousedown":to=!0;break;case"contextmenu":case"mouseup":case"dragend":to=!1,ia(m,n,p);break;case"selectionchange":if(Ch)break;case"keydown":case"keyup":ia(m,n,p)}var v;if(Jo)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else tn?qu(e,n)&&(y="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(y="onCompositionStart");y&&(Qu&&n.locale!=="ko"&&(tn||y!=="onCompositionStart"?y==="onCompositionEnd"&&tn&&(v=Hu()):(vt=p,Yo="value"in vt?vt.value:vt.textContent,tn=!0)),b=xs(c,y),0<b.length&&(y=new Xl(y,e,null,n,p),m.push({event:y,listeners:b}),v?y.data=v:(v=Ku(n),v!==null&&(y.data=v)))),(v=hh?ph(e,n):mh(e,n))&&(c=xs(c,"onBeforeInput"),0<c.length&&(p=new Xl("onBeforeInput","beforeinput",null,n,p),m.push({event:p,listeners:c}),p.data=v))}oc(m,t)})}function hr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function xs(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=or(e,n),i!=null&&r.unshift(hr(e,i,s)),i=or(e,t),i!=null&&r.push(hr(e,i,s))),e=e.return}return r}function Jt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function aa(e,t,n,r,s){for(var i=t._reactName,l=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,s?(u=or(n,i),u!=null&&l.unshift(hr(n,u,a))):s||(u=or(n,i),u!=null&&l.push(hr(n,u,a)))),n=n.return}l.length!==0&&e.push({event:t,listeners:l})}var Lh=/\r\n?/g,Th=/\u0000|\uFFFD/g;function ua(e){return(typeof e=="string"?e:""+e).replace(Lh,`
`).replace(Th,"")}function Dr(e,t,n){if(t=ua(t),ua(e)!==t&&n)throw Error(L(425))}function ws(){}var no=null,ro=null;function so(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var io=typeof setTimeout=="function"?setTimeout:void 0,Ph=typeof clearTimeout=="function"?clearTimeout:void 0,ca=typeof Promise=="function"?Promise:void 0,Mh=typeof queueMicrotask=="function"?queueMicrotask:typeof ca<"u"?function(e){return ca.resolve(null).then(e).catch(Rh)}:io;function Rh(e){setTimeout(function(){throw e})}function vi(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),ur(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);ur(t)}function St(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function da(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var bn=Math.random().toString(36).slice(2),Ge="__reactFiber$"+bn,pr="__reactProps$"+bn,at="__reactContainer$"+bn,oo="__reactEvents$"+bn,Oh="__reactListeners$"+bn,Ah="__reactHandles$"+bn;function Bt(e){var t=e[Ge];if(t)return t;for(var n=e.parentNode;n;){if(t=n[at]||n[Ge]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=da(e);e!==null;){if(n=e[Ge])return n;e=da(e)}return t}e=n,n=e.parentNode}return null}function Cr(e){return e=e[Ge]||e[at],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function sn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(L(33))}function Us(e){return e[pr]||null}var lo=[],on=-1;function Ot(e){return{current:e}}function K(e){0>on||(e.current=lo[on],lo[on]=null,on--)}function Q(e,t){on++,lo[on]=e.current,e.current=t}var Mt={},me=Ot(Mt),je=Ot(!1),Ht=Mt;function wn(e,t){var n=e.type.contextTypes;if(!n)return Mt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ne(e){return e=e.childContextTypes,e!=null}function ks(){K(je),K(me)}function fa(e,t,n){if(me.current!==Mt)throw Error(L(168));Q(me,t),Q(je,n)}function ac(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(L(108,xf(e)||"Unknown",s));return Z({},n,r)}function js(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Mt,Ht=me.current,Q(me,e),Q(je,je.current),!0}function ha(e,t,n){var r=e.stateNode;if(!r)throw Error(L(169));n?(e=ac(e,t,Ht),r.__reactInternalMemoizedMergedChildContext=e,K(je),K(me),Q(me,e)):K(je),Q(je,n)}var rt=null,Vs=!1,xi=!1;function uc(e){rt===null?rt=[e]:rt.push(e)}function zh(e){Vs=!0,uc(e)}function At(){if(!xi&&rt!==null){xi=!0;var e=0,t=H;try{var n=rt;for(H=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}rt=null,Vs=!1}catch(s){throw rt!==null&&(rt=rt.slice(e+1)),Ou(Ho,At),s}finally{H=t,xi=!1}}return null}var ln=[],an=0,Ns=null,Ss=0,Re=[],Oe=0,Qt=null,st=1,it="";function Ft(e,t){ln[an++]=Ss,ln[an++]=Ns,Ns=e,Ss=t}function cc(e,t,n){Re[Oe++]=st,Re[Oe++]=it,Re[Oe++]=Qt,Qt=e;var r=st;e=it;var s=32-He(r)-1;r&=~(1<<s),n+=1;var i=32-He(t)+s;if(30<i){var l=s-s%5;i=(r&(1<<l)-1).toString(32),r>>=l,s-=l,st=1<<32-He(t)+s|n<<s|r,it=i+e}else st=1<<i|n<<s|r,it=e}function el(e){e.return!==null&&(Ft(e,1),cc(e,1,0))}function tl(e){for(;e===Ns;)Ns=ln[--an],ln[an]=null,Ss=ln[--an],ln[an]=null;for(;e===Qt;)Qt=Re[--Oe],Re[Oe]=null,it=Re[--Oe],Re[Oe]=null,st=Re[--Oe],Re[Oe]=null}var Le=null,be=null,Y=!1,We=null;function dc(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function pa(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Le=e,be=St(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Le=e,be=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Qt!==null?{id:st,overflow:it}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Le=e,be=null,!0):!1;default:return!1}}function ao(e){return(e.mode&1)!==0&&(e.flags&128)===0}function uo(e){if(Y){var t=be;if(t){var n=t;if(!pa(e,t)){if(ao(e))throw Error(L(418));t=St(n.nextSibling);var r=Le;t&&pa(e,t)?dc(r,n):(e.flags=e.flags&-4097|2,Y=!1,Le=e)}}else{if(ao(e))throw Error(L(418));e.flags=e.flags&-4097|2,Y=!1,Le=e}}}function ma(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Le=e}function Br(e){if(e!==Le)return!1;if(!Y)return ma(e),Y=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!so(e.type,e.memoizedProps)),t&&(t=be)){if(ao(e))throw fc(),Error(L(418));for(;t;)dc(e,t),t=St(t.nextSibling)}if(ma(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(L(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){be=St(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}be=null}}else be=Le?St(e.stateNode.nextSibling):null;return!0}function fc(){for(var e=be;e;)e=St(e.nextSibling)}function kn(){be=Le=null,Y=!1}function nl(e){We===null?We=[e]:We.push(e)}var Ih=dt.ReactCurrentBatchConfig;function Fn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(L(309));var r=n.stateNode}if(!r)throw Error(L(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(l){var a=s.refs;l===null?delete a[i]:a[i]=l},t._stringRef=i,t)}if(typeof e!="string")throw Error(L(284));if(!n._owner)throw Error(L(290,e))}return e}function $r(e,t){throw e=Object.prototype.toString.call(t),Error(L(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ga(e){var t=e._init;return t(e._payload)}function hc(e){function t(f,d){if(e){var h=f.deletions;h===null?(f.deletions=[d],f.flags|=16):h.push(d)}}function n(f,d){if(!e)return null;for(;d!==null;)t(f,d),d=d.sibling;return null}function r(f,d){for(f=new Map;d!==null;)d.key!==null?f.set(d.key,d):f.set(d.index,d),d=d.sibling;return f}function s(f,d){return f=bt(f,d),f.index=0,f.sibling=null,f}function i(f,d,h){return f.index=h,e?(h=f.alternate,h!==null?(h=h.index,h<d?(f.flags|=2,d):h):(f.flags|=2,d)):(f.flags|=1048576,d)}function l(f){return e&&f.alternate===null&&(f.flags|=2),f}function a(f,d,h,w){return d===null||d.tag!==6?(d=Ei(h,f.mode,w),d.return=f,d):(d=s(d,h),d.return=f,d)}function u(f,d,h,w){var N=h.type;return N===en?p(f,d,h.props.children,w,h.key):d!==null&&(d.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===pt&&ga(N)===d.type)?(w=s(d,h.props),w.ref=Fn(f,d,h),w.return=f,w):(w=ls(h.type,h.key,h.props,null,f.mode,w),w.ref=Fn(f,d,h),w.return=f,w)}function c(f,d,h,w){return d===null||d.tag!==4||d.stateNode.containerInfo!==h.containerInfo||d.stateNode.implementation!==h.implementation?(d=_i(h,f.mode,w),d.return=f,d):(d=s(d,h.children||[]),d.return=f,d)}function p(f,d,h,w,N){return d===null||d.tag!==7?(d=Wt(h,f.mode,w,N),d.return=f,d):(d=s(d,h),d.return=f,d)}function m(f,d,h){if(typeof d=="string"&&d!==""||typeof d=="number")return d=Ei(""+d,f.mode,h),d.return=f,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Tr:return h=ls(d.type,d.key,d.props,null,f.mode,h),h.ref=Fn(f,null,d),h.return=f,h;case Zt:return d=_i(d,f.mode,h),d.return=f,d;case pt:var w=d._init;return m(f,w(d._payload),h)}if(Vn(d)||Rn(d))return d=Wt(d,f.mode,h,null),d.return=f,d;$r(f,d)}return null}function g(f,d,h,w){var N=d!==null?d.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return N!==null?null:a(f,d,""+h,w);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Tr:return h.key===N?u(f,d,h,w):null;case Zt:return h.key===N?c(f,d,h,w):null;case pt:return N=h._init,g(f,d,N(h._payload),w)}if(Vn(h)||Rn(h))return N!==null?null:p(f,d,h,w,null);$r(f,h)}return null}function j(f,d,h,w,N){if(typeof w=="string"&&w!==""||typeof w=="number")return f=f.get(h)||null,a(d,f,""+w,N);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case Tr:return f=f.get(w.key===null?h:w.key)||null,u(d,f,w,N);case Zt:return f=f.get(w.key===null?h:w.key)||null,c(d,f,w,N);case pt:var b=w._init;return j(f,d,h,b(w._payload),N)}if(Vn(w)||Rn(w))return f=f.get(h)||null,p(d,f,w,N,null);$r(d,w)}return null}function x(f,d,h,w){for(var N=null,b=null,v=d,y=d=0,C=null;v!==null&&y<h.length;y++){v.index>y?(C=v,v=null):C=v.sibling;var E=g(f,v,h[y],w);if(E===null){v===null&&(v=C);break}e&&v&&E.alternate===null&&t(f,v),d=i(E,d,y),b===null?N=E:b.sibling=E,b=E,v=C}if(y===h.length)return n(f,v),Y&&Ft(f,y),N;if(v===null){for(;y<h.length;y++)v=m(f,h[y],w),v!==null&&(d=i(v,d,y),b===null?N=v:b.sibling=v,b=v);return Y&&Ft(f,y),N}for(v=r(f,v);y<h.length;y++)C=j(v,f,y,h[y],w),C!==null&&(e&&C.alternate!==null&&v.delete(C.key===null?y:C.key),d=i(C,d,y),b===null?N=C:b.sibling=C,b=C);return e&&v.forEach(function(S){return t(f,S)}),Y&&Ft(f,y),N}function _(f,d,h,w){var N=Rn(h);if(typeof N!="function")throw Error(L(150));if(h=N.call(h),h==null)throw Error(L(151));for(var b=N=null,v=d,y=d=0,C=null,E=h.next();v!==null&&!E.done;y++,E=h.next()){v.index>y?(C=v,v=null):C=v.sibling;var S=g(f,v,E.value,w);if(S===null){v===null&&(v=C);break}e&&v&&S.alternate===null&&t(f,v),d=i(S,d,y),b===null?N=S:b.sibling=S,b=S,v=C}if(E.done)return n(f,v),Y&&Ft(f,y),N;if(v===null){for(;!E.done;y++,E=h.next())E=m(f,E.value,w),E!==null&&(d=i(E,d,y),b===null?N=E:b.sibling=E,b=E);return Y&&Ft(f,y),N}for(v=r(f,v);!E.done;y++,E=h.next())E=j(v,f,y,E.value,w),E!==null&&(e&&E.alternate!==null&&v.delete(E.key===null?y:E.key),d=i(E,d,y),b===null?N=E:b.sibling=E,b=E);return e&&v.forEach(function(O){return t(f,O)}),Y&&Ft(f,y),N}function R(f,d,h,w){if(typeof h=="object"&&h!==null&&h.type===en&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case Tr:e:{for(var N=h.key,b=d;b!==null;){if(b.key===N){if(N=h.type,N===en){if(b.tag===7){n(f,b.sibling),d=s(b,h.props.children),d.return=f,f=d;break e}}else if(b.elementType===N||typeof N=="object"&&N!==null&&N.$$typeof===pt&&ga(N)===b.type){n(f,b.sibling),d=s(b,h.props),d.ref=Fn(f,b,h),d.return=f,f=d;break e}n(f,b);break}else t(f,b);b=b.sibling}h.type===en?(d=Wt(h.props.children,f.mode,w,h.key),d.return=f,f=d):(w=ls(h.type,h.key,h.props,null,f.mode,w),w.ref=Fn(f,d,h),w.return=f,f=w)}return l(f);case Zt:e:{for(b=h.key;d!==null;){if(d.key===b)if(d.tag===4&&d.stateNode.containerInfo===h.containerInfo&&d.stateNode.implementation===h.implementation){n(f,d.sibling),d=s(d,h.children||[]),d.return=f,f=d;break e}else{n(f,d);break}else t(f,d);d=d.sibling}d=_i(h,f.mode,w),d.return=f,f=d}return l(f);case pt:return b=h._init,R(f,d,b(h._payload),w)}if(Vn(h))return x(f,d,h,w);if(Rn(h))return _(f,d,h,w);$r(f,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,d!==null&&d.tag===6?(n(f,d.sibling),d=s(d,h),d.return=f,f=d):(n(f,d),d=Ei(h,f.mode,w),d.return=f,f=d),l(f)):n(f,d)}return R}var jn=hc(!0),pc=hc(!1),Cs=Ot(null),Es=null,un=null,rl=null;function sl(){rl=un=Es=null}function il(e){var t=Cs.current;K(Cs),e._currentValue=t}function co(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function yn(e,t){Es=e,rl=un=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function Fe(e){var t=e._currentValue;if(rl!==e)if(e={context:e,memoizedValue:t,next:null},un===null){if(Es===null)throw Error(L(308));un=e,Es.dependencies={lanes:0,firstContext:e}}else un=un.next=e;return t}var $t=null;function ol(e){$t===null?$t=[e]:$t.push(e)}function mc(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,ol(t)):(n.next=s.next,s.next=n),t.interleaved=n,ut(e,r)}function ut(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var mt=!1;function ll(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function gc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ot(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ct(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,W&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,ut(e,n)}return s=r.interleaved,s===null?(t.next=t,ol(r)):(t.next=s.next,s.next=t),r.interleaved=t,ut(e,n)}function ts(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Qo(e,n)}}function ya(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=l:i=i.next=l,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function _s(e,t,n,r){var s=e.updateQueue;mt=!1;var i=s.firstBaseUpdate,l=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var u=a,c=u.next;u.next=null,l===null?i=c:l.next=c,l=u;var p=e.alternate;p!==null&&(p=p.updateQueue,a=p.lastBaseUpdate,a!==l&&(a===null?p.firstBaseUpdate=c:a.next=c,p.lastBaseUpdate=u))}if(i!==null){var m=s.baseState;l=0,p=c=u=null,a=i;do{var g=a.lane,j=a.eventTime;if((r&g)===g){p!==null&&(p=p.next={eventTime:j,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,_=a;switch(g=t,j=n,_.tag){case 1:if(x=_.payload,typeof x=="function"){m=x.call(j,m,g);break e}m=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=_.payload,g=typeof x=="function"?x.call(j,m,g):x,g==null)break e;m=Z({},m,g);break e;case 2:mt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,g=s.effects,g===null?s.effects=[a]:g.push(a))}else j={eventTime:j,lane:g,tag:a.tag,payload:a.payload,callback:a.callback,next:null},p===null?(c=p=j,u=m):p=p.next=j,l|=g;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;g=a,a=g.next,g.next=null,s.lastBaseUpdate=g,s.shared.pending=null}}while(1);if(p===null&&(u=m),s.baseState=u,s.firstBaseUpdate=c,s.lastBaseUpdate=p,t=s.shared.interleaved,t!==null){s=t;do l|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);Kt|=l,e.lanes=l,e.memoizedState=m}}function va(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(L(191,s));s.call(r)}}}var Er={},Ze=Ot(Er),mr=Ot(Er),gr=Ot(Er);function Ut(e){if(e===Er)throw Error(L(174));return e}function al(e,t){switch(Q(gr,t),Q(mr,e),Q(Ze,Er),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Wi(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Wi(t,e)}K(Ze),Q(Ze,t)}function Nn(){K(Ze),K(mr),K(gr)}function yc(e){Ut(gr.current);var t=Ut(Ze.current),n=Wi(t,e.type);t!==n&&(Q(mr,e),Q(Ze,n))}function ul(e){mr.current===e&&(K(Ze),K(mr))}var G=Ot(0);function bs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var wi=[];function cl(){for(var e=0;e<wi.length;e++)wi[e]._workInProgressVersionPrimary=null;wi.length=0}var ns=dt.ReactCurrentDispatcher,ki=dt.ReactCurrentBatchConfig,qt=0,J=null,ie=null,le=null,Ls=!1,Zn=!1,yr=0,Fh=0;function fe(){throw Error(L(321))}function dl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!qe(e[n],t[n]))return!1;return!0}function fl(e,t,n,r,s,i){if(qt=i,J=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ns.current=e===null||e.memoizedState===null?Uh:Vh,e=n(r,s),Zn){i=0;do{if(Zn=!1,yr=0,25<=i)throw Error(L(301));i+=1,le=ie=null,t.updateQueue=null,ns.current=Wh,e=n(r,s)}while(Zn)}if(ns.current=Ts,t=ie!==null&&ie.next!==null,qt=0,le=ie=J=null,Ls=!1,t)throw Error(L(300));return e}function hl(){var e=yr!==0;return yr=0,e}function Xe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return le===null?J.memoizedState=le=e:le=le.next=e,le}function De(){if(ie===null){var e=J.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=le===null?J.memoizedState:le.next;if(t!==null)le=t,ie=e;else{if(e===null)throw Error(L(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},le===null?J.memoizedState=le=e:le=le.next=e}return le}function vr(e,t){return typeof t=="function"?t(e):t}function ji(e){var t=De(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=ie,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var l=s.next;s.next=i.next,i.next=l}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=l=null,u=null,c=i;do{var p=c.lane;if((qt&p)===p)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var m={lane:p,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=m,l=r):u=u.next=m,J.lanes|=p,Kt|=p}c=c.next}while(c!==null&&c!==i);u===null?l=r:u.next=a,qe(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=l,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,J.lanes|=i,Kt|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ni(e){var t=De(),n=t.queue;if(n===null)throw Error(L(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var l=s=s.next;do i=e(i,l.action),l=l.next;while(l!==s);qe(i,t.memoizedState)||(ke=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function vc(){}function xc(e,t){var n=J,r=De(),s=t(),i=!qe(r.memoizedState,s);if(i&&(r.memoizedState=s,ke=!0),r=r.queue,pl(jc.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||le!==null&&le.memoizedState.tag&1){if(n.flags|=2048,xr(9,kc.bind(null,n,r,s,t),void 0,null),ae===null)throw Error(L(349));qt&30||wc(n,t,s)}return s}function wc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=J.updateQueue,t===null?(t={lastEffect:null,stores:null},J.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function kc(e,t,n,r){t.value=n,t.getSnapshot=r,Nc(t)&&Sc(e)}function jc(e,t,n){return n(function(){Nc(t)&&Sc(e)})}function Nc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qe(e,n)}catch{return!0}}function Sc(e){var t=ut(e,1);t!==null&&Qe(t,e,1,-1)}function xa(e){var t=Xe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:vr,lastRenderedState:e},t.queue=e,e=e.dispatch=$h.bind(null,J,e),[t.memoizedState,e]}function xr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=J.updateQueue,t===null?(t={lastEffect:null,stores:null},J.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Cc(){return De().memoizedState}function rs(e,t,n,r){var s=Xe();J.flags|=e,s.memoizedState=xr(1|t,n,void 0,r===void 0?null:r)}function Ws(e,t,n,r){var s=De();r=r===void 0?null:r;var i=void 0;if(ie!==null){var l=ie.memoizedState;if(i=l.destroy,r!==null&&dl(r,l.deps)){s.memoizedState=xr(t,n,i,r);return}}J.flags|=e,s.memoizedState=xr(1|t,n,i,r)}function wa(e,t){return rs(8390656,8,e,t)}function pl(e,t){return Ws(2048,8,e,t)}function Ec(e,t){return Ws(4,2,e,t)}function _c(e,t){return Ws(4,4,e,t)}function bc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Lc(e,t,n){return n=n!=null?n.concat([e]):null,Ws(4,4,bc.bind(null,t,e),n)}function ml(){}function Tc(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&dl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Pc(e,t){var n=De();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&dl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Mc(e,t,n){return qt&21?(qe(n,t)||(n=Iu(),J.lanes|=n,Kt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function Dh(e,t){var n=H;H=n!==0&&4>n?n:4,e(!0);var r=ki.transition;ki.transition={};try{e(!1),t()}finally{H=n,ki.transition=r}}function Rc(){return De().memoizedState}function Bh(e,t,n){var r=_t(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Oc(e))Ac(t,n);else if(n=mc(e,t,n,r),n!==null){var s=ye();Qe(n,e,r,s),zc(n,t,r)}}function $h(e,t,n){var r=_t(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Oc(e))Ac(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var l=t.lastRenderedState,a=i(l,n);if(s.hasEagerState=!0,s.eagerState=a,qe(a,l)){var u=t.interleaved;u===null?(s.next=s,ol(t)):(s.next=u.next,u.next=s),t.interleaved=s;return}}catch{}finally{}n=mc(e,t,s,r),n!==null&&(s=ye(),Qe(n,e,r,s),zc(n,t,r))}}function Oc(e){var t=e.alternate;return e===J||t!==null&&t===J}function Ac(e,t){Zn=Ls=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function zc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Qo(e,n)}}var Ts={readContext:Fe,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},Uh={readContext:Fe,useCallback:function(e,t){return Xe().memoizedState=[e,t===void 0?null:t],e},useContext:Fe,useEffect:wa,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,rs(4194308,4,bc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return rs(4194308,4,e,t)},useInsertionEffect:function(e,t){return rs(4,2,e,t)},useMemo:function(e,t){var n=Xe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Xe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Bh.bind(null,J,e),[r.memoizedState,e]},useRef:function(e){var t=Xe();return e={current:e},t.memoizedState=e},useState:xa,useDebugValue:ml,useDeferredValue:function(e){return Xe().memoizedState=e},useTransition:function(){var e=xa(!1),t=e[0];return e=Dh.bind(null,e[1]),Xe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=J,s=Xe();if(Y){if(n===void 0)throw Error(L(407));n=n()}else{if(n=t(),ae===null)throw Error(L(349));qt&30||wc(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,wa(jc.bind(null,r,i,e),[e]),r.flags|=2048,xr(9,kc.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Xe(),t=ae.identifierPrefix;if(Y){var n=it,r=st;n=(r&~(1<<32-He(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=yr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Fh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Vh={readContext:Fe,useCallback:Tc,useContext:Fe,useEffect:pl,useImperativeHandle:Lc,useInsertionEffect:Ec,useLayoutEffect:_c,useMemo:Pc,useReducer:ji,useRef:Cc,useState:function(){return ji(vr)},useDebugValue:ml,useDeferredValue:function(e){var t=De();return Mc(t,ie.memoizedState,e)},useTransition:function(){var e=ji(vr)[0],t=De().memoizedState;return[e,t]},useMutableSource:vc,useSyncExternalStore:xc,useId:Rc,unstable_isNewReconciler:!1},Wh={readContext:Fe,useCallback:Tc,useContext:Fe,useEffect:pl,useImperativeHandle:Lc,useInsertionEffect:Ec,useLayoutEffect:_c,useMemo:Pc,useReducer:Ni,useRef:Cc,useState:function(){return Ni(vr)},useDebugValue:ml,useDeferredValue:function(e){var t=De();return ie===null?t.memoizedState=e:Mc(t,ie.memoizedState,e)},useTransition:function(){var e=Ni(vr)[0],t=De().memoizedState;return[e,t]},useMutableSource:vc,useSyncExternalStore:xc,useId:Rc,unstable_isNewReconciler:!1};function $e(e,t){if(e&&e.defaultProps){t=Z({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function fo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Z({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Hs={isMounted:function(e){return(e=e._reactInternals)?Gt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ye(),s=_t(e),i=ot(r,s);i.payload=t,n!=null&&(i.callback=n),t=Ct(e,i,s),t!==null&&(Qe(t,e,s,r),ts(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ye(),s=_t(e),i=ot(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Ct(e,i,s),t!==null&&(Qe(t,e,s,r),ts(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ye(),r=_t(e),s=ot(n,r);s.tag=2,t!=null&&(s.callback=t),t=Ct(e,s,r),t!==null&&(Qe(t,e,r,n),ts(t,e,r))}};function ka(e,t,n,r,s,i,l){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,l):t.prototype&&t.prototype.isPureReactComponent?!dr(n,r)||!dr(s,i):!0}function Ic(e,t,n){var r=!1,s=Mt,i=t.contextType;return typeof i=="object"&&i!==null?i=Fe(i):(s=Ne(t)?Ht:me.current,r=t.contextTypes,i=(r=r!=null)?wn(e,s):Mt),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Hs,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function ja(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Hs.enqueueReplaceState(t,t.state,null)}function ho(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},ll(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=Fe(i):(i=Ne(t)?Ht:me.current,s.context=wn(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(fo(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Hs.enqueueReplaceState(s,s.state,null),_s(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Sn(e,t){try{var n="",r=t;do n+=vf(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function Si(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function po(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Hh=typeof WeakMap=="function"?WeakMap:Map;function Fc(e,t,n){n=ot(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ms||(Ms=!0,So=r),po(e,t)},n}function Dc(e,t,n){n=ot(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){po(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){po(e,t),typeof r!="function"&&(Et===null?Et=new Set([this]):Et.add(this));var l=t.stack;this.componentDidCatch(t.value,{componentStack:l!==null?l:""})}),n}function Na(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Hh;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=ip.bind(null,e,t,n),t.then(e,e))}function Sa(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Ca(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ot(-1,1),t.tag=2,Ct(n,t,1))),n.lanes|=1),e)}var Qh=dt.ReactCurrentOwner,ke=!1;function ge(e,t,n,r){t.child=e===null?pc(t,null,n,r):jn(t,e.child,n,r)}function Ea(e,t,n,r,s){n=n.render;var i=t.ref;return yn(t,s),r=fl(e,t,n,r,i,s),n=hl(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,ct(e,t,s)):(Y&&n&&el(t),t.flags|=1,ge(e,t,r,s),t.child)}function _a(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Nl(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Bc(e,t,i,r,s)):(e=ls(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var l=i.memoizedProps;if(n=n.compare,n=n!==null?n:dr,n(l,r)&&e.ref===t.ref)return ct(e,t,s)}return t.flags|=1,e=bt(i,r),e.ref=t.ref,e.return=t,t.child=e}function Bc(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(dr(i,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,ct(e,t,s)}return mo(e,t,n,r,s)}function $c(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Q(dn,Ee),Ee|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Q(dn,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,Q(dn,Ee),Ee|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,Q(dn,Ee),Ee|=r;return ge(e,t,s,n),t.child}function Uc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function mo(e,t,n,r,s){var i=Ne(n)?Ht:me.current;return i=wn(t,i),yn(t,s),n=fl(e,t,n,r,i,s),r=hl(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,ct(e,t,s)):(Y&&r&&el(t),t.flags|=1,ge(e,t,n,s),t.child)}function ba(e,t,n,r,s){if(Ne(n)){var i=!0;js(t)}else i=!1;if(yn(t,s),t.stateNode===null)ss(e,t),Ic(t,n,r),ho(t,n,r,s),r=!0;else if(e===null){var l=t.stateNode,a=t.memoizedProps;l.props=a;var u=l.context,c=n.contextType;typeof c=="object"&&c!==null?c=Fe(c):(c=Ne(n)?Ht:me.current,c=wn(t,c));var p=n.getDerivedStateFromProps,m=typeof p=="function"||typeof l.getSnapshotBeforeUpdate=="function";m||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(a!==r||u!==c)&&ja(t,l,r,c),mt=!1;var g=t.memoizedState;l.state=g,_s(t,r,l,s),u=t.memoizedState,a!==r||g!==u||je.current||mt?(typeof p=="function"&&(fo(t,n,p,r),u=t.memoizedState),(a=mt||ka(t,n,a,r,g,u,c))?(m||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount()),typeof l.componentDidMount=="function"&&(t.flags|=4194308)):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=c,r=a):(typeof l.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,gc(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:$e(t.type,a),l.props=c,m=t.pendingProps,g=l.context,u=n.contextType,typeof u=="object"&&u!==null?u=Fe(u):(u=Ne(n)?Ht:me.current,u=wn(t,u));var j=n.getDerivedStateFromProps;(p=typeof j=="function"||typeof l.getSnapshotBeforeUpdate=="function")||typeof l.UNSAFE_componentWillReceiveProps!="function"&&typeof l.componentWillReceiveProps!="function"||(a!==m||g!==u)&&ja(t,l,r,u),mt=!1,g=t.memoizedState,l.state=g,_s(t,r,l,s);var x=t.memoizedState;a!==m||g!==x||je.current||mt?(typeof j=="function"&&(fo(t,n,j,r),x=t.memoizedState),(c=mt||ka(t,n,c,r,g,x,u)||!1)?(p||typeof l.UNSAFE_componentWillUpdate!="function"&&typeof l.componentWillUpdate!="function"||(typeof l.componentWillUpdate=="function"&&l.componentWillUpdate(r,x,u),typeof l.UNSAFE_componentWillUpdate=="function"&&l.UNSAFE_componentWillUpdate(r,x,u)),typeof l.componentDidUpdate=="function"&&(t.flags|=4),typeof l.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof l.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),l.props=r,l.state=x,l.context=u,r=c):(typeof l.componentDidUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof l.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),r=!1)}return go(e,t,n,r,i,s)}function go(e,t,n,r,s,i){Uc(e,t);var l=(t.flags&128)!==0;if(!r&&!l)return s&&ha(t,n,!1),ct(e,t,i);r=t.stateNode,Qh.current=t;var a=l&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&l?(t.child=jn(t,e.child,null,i),t.child=jn(t,null,a,i)):ge(e,t,a,i),t.memoizedState=r.state,s&&ha(t,n,!0),t.child}function Vc(e){var t=e.stateNode;t.pendingContext?fa(e,t.pendingContext,t.pendingContext!==t.context):t.context&&fa(e,t.context,!1),al(e,t.containerInfo)}function La(e,t,n,r,s){return kn(),nl(s),t.flags|=256,ge(e,t,n,r),t.child}var yo={dehydrated:null,treeContext:null,retryLane:0};function vo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Wc(e,t,n){var r=t.pendingProps,s=G.current,i=!1,l=(t.flags&128)!==0,a;if((a=l)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),Q(G,s&1),e===null)return uo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,i?(r=t.mode,i=t.child,l={mode:"hidden",children:l},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=l):i=Ks(l,r,0,null),e=Wt(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=vo(n),t.memoizedState=yo,e):gl(t,l));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return qh(e,t,l,r,a,s,n);if(i){i=r.fallback,l=t.mode,s=e.child,a=s.sibling;var u={mode:"hidden",children:r.children};return!(l&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=bt(s,u),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=bt(a,i):(i=Wt(i,l,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,l=e.child.memoizedState,l=l===null?vo(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},i.memoizedState=l,i.childLanes=e.childLanes&~n,t.memoizedState=yo,r}return i=e.child,e=i.sibling,r=bt(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function gl(e,t){return t=Ks({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ur(e,t,n,r){return r!==null&&nl(r),jn(t,e.child,null,n),e=gl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function qh(e,t,n,r,s,i,l){if(n)return t.flags&256?(t.flags&=-257,r=Si(Error(L(422))),Ur(e,t,l,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=Ks({mode:"visible",children:r.children},s,0,null),i=Wt(i,s,l,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&jn(t,e.child,null,l),t.child.memoizedState=vo(l),t.memoizedState=yo,i);if(!(t.mode&1))return Ur(e,t,l,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(L(419)),r=Si(i,r,void 0),Ur(e,t,l,r)}if(a=(l&e.childLanes)!==0,ke||a){if(r=ae,r!==null){switch(l&-l){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|l)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,ut(e,s),Qe(r,e,s,-1))}return jl(),r=Si(Error(L(421))),Ur(e,t,l,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=op.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,be=St(s.nextSibling),Le=t,Y=!0,We=null,e!==null&&(Re[Oe++]=st,Re[Oe++]=it,Re[Oe++]=Qt,st=e.id,it=e.overflow,Qt=t),t=gl(t,r.children),t.flags|=4096,t)}function Ta(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),co(e.return,t,n)}function Ci(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Hc(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(ge(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ta(e,n,t);else if(e.tag===19)Ta(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Q(G,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&bs(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),Ci(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&bs(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}Ci(t,!0,n,null,i);break;case"together":Ci(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ss(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ct(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Kt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(L(153));if(t.child!==null){for(e=t.child,n=bt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=bt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Kh(e,t,n){switch(t.tag){case 3:Vc(t),kn();break;case 5:yc(t);break;case 1:Ne(t.type)&&js(t);break;case 4:al(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;Q(Cs,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Q(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?Wc(e,t,n):(Q(G,G.current&1),e=ct(e,t,n),e!==null?e.sibling:null);Q(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Hc(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),Q(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,$c(e,t,n)}return ct(e,t,n)}var Qc,xo,qc,Kc;Qc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};xo=function(){};qc=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Ut(Ze.current);var i=null;switch(n){case"input":s=Bi(e,s),r=Bi(e,r),i=[];break;case"select":s=Z({},s,{value:void 0}),r=Z({},r,{value:void 0}),i=[];break;case"textarea":s=Vi(e,s),r=Vi(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ws)}Hi(n,r);var l;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(l in a)a.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(sr.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(a=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(l in a)!a.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&a[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(i||(i=[]),i.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(i=i||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(i=i||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(sr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&q("scroll",e),i||a===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};Kc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Dn(e,t){if(!Y)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function he(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Yh(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return he(t),null;case 1:return Ne(t.type)&&ks(),he(t),null;case 3:return r=t.stateNode,Nn(),K(je),K(me),cl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Br(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,We!==null&&(_o(We),We=null))),xo(e,t),he(t),null;case 5:ul(t);var s=Ut(gr.current);if(n=t.type,e!==null&&t.stateNode!=null)qc(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(L(166));return he(t),null}if(e=Ut(Ze.current),Br(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[Ge]=t,r[pr]=i,e=(t.mode&1)!==0,n){case"dialog":q("cancel",r),q("close",r);break;case"iframe":case"object":case"embed":q("load",r);break;case"video":case"audio":for(s=0;s<Hn.length;s++)q(Hn[s],r);break;case"source":q("error",r);break;case"img":case"image":case"link":q("error",r),q("load",r);break;case"details":q("toggle",r);break;case"input":Dl(r,i),q("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},q("invalid",r);break;case"textarea":$l(r,i),q("invalid",r)}Hi(n,i),s=null;for(var l in i)if(i.hasOwnProperty(l)){var a=i[l];l==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Dr(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Dr(r.textContent,a,e),s=["children",""+a]):sr.hasOwnProperty(l)&&a!=null&&l==="onScroll"&&q("scroll",r)}switch(n){case"input":Pr(r),Bl(r,i,!0);break;case"textarea":Pr(r),Ul(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=ws)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{l=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ju(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),n==="select"&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[Ge]=t,e[pr]=r,Qc(e,t,!1,!1),t.stateNode=e;e:{switch(l=Qi(n,r),n){case"dialog":q("cancel",e),q("close",e),s=r;break;case"iframe":case"object":case"embed":q("load",e),s=r;break;case"video":case"audio":for(s=0;s<Hn.length;s++)q(Hn[s],e);s=r;break;case"source":q("error",e),s=r;break;case"img":case"image":case"link":q("error",e),q("load",e),s=r;break;case"details":q("toggle",e),s=r;break;case"input":Dl(e,r),s=Bi(e,r),q("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Z({},r,{value:void 0}),q("invalid",e);break;case"textarea":$l(e,r),s=Vi(e,r),q("invalid",e);break;default:s=r}Hi(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var u=a[i];i==="style"?Cu(e,u):i==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Nu(e,u)):i==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&ir(e,u):typeof u=="number"&&ir(e,""+u):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(sr.hasOwnProperty(i)?u!=null&&i==="onScroll"&&q("scroll",e):u!=null&&Bo(e,i,u,l))}switch(n){case"input":Pr(e),Bl(e,r,!1);break;case"textarea":Pr(e),Ul(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Pt(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?hn(e,!!r.multiple,i,!1):r.defaultValue!=null&&hn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=ws)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return he(t),null;case 6:if(e&&t.stateNode!=null)Kc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(L(166));if(n=Ut(gr.current),Ut(Ze.current),Br(t)){if(r=t.stateNode,n=t.memoizedProps,r[Ge]=t,(i=r.nodeValue!==n)&&(e=Le,e!==null))switch(e.tag){case 3:Dr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Dr(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Ge]=t,t.stateNode=r}return he(t),null;case 13:if(K(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Y&&be!==null&&t.mode&1&&!(t.flags&128))fc(),kn(),t.flags|=98560,i=!1;else if(i=Br(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(L(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(L(317));i[Ge]=t}else kn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;he(t),i=!1}else We!==null&&(_o(We),We=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?oe===0&&(oe=3):jl())),t.updateQueue!==null&&(t.flags|=4),he(t),null);case 4:return Nn(),xo(e,t),e===null&&fr(t.stateNode.containerInfo),he(t),null;case 10:return il(t.type._context),he(t),null;case 17:return Ne(t.type)&&ks(),he(t),null;case 19:if(K(G),i=t.memoizedState,i===null)return he(t),null;if(r=(t.flags&128)!==0,l=i.rendering,l===null)if(r)Dn(i,!1);else{if(oe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(l=bs(e),l!==null){for(t.flags|=128,Dn(i,!1),r=l.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,l=i.alternate,l===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Q(G,G.current&1|2),t.child}e=e.sibling}i.tail!==null&&ne()>Cn&&(t.flags|=128,r=!0,Dn(i,!1),t.lanes=4194304)}else{if(!r)if(e=bs(l),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Dn(i,!0),i.tail===null&&i.tailMode==="hidden"&&!l.alternate&&!Y)return he(t),null}else 2*ne()-i.renderingStartTime>Cn&&n!==1073741824&&(t.flags|=128,r=!0,Dn(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(n=i.last,n!==null?n.sibling=l:t.child=l,i.last=l)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ne(),t.sibling=null,n=G.current,Q(G,r?n&1|2:n&1),t):(he(t),null);case 22:case 23:return kl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(he(t),t.subtreeFlags&6&&(t.flags|=8192)):he(t),null;case 24:return null;case 25:return null}throw Error(L(156,t.tag))}function Xh(e,t){switch(tl(t),t.tag){case 1:return Ne(t.type)&&ks(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Nn(),K(je),K(me),cl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ul(t),null;case 13:if(K(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(L(340));kn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(G),null;case 4:return Nn(),null;case 10:return il(t.type._context),null;case 22:case 23:return kl(),null;case 24:return null;default:return null}}var Vr=!1,pe=!1,Gh=typeof WeakSet=="function"?WeakSet:Set,z=null;function cn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){te(e,t,r)}else n.current=null}function wo(e,t,n){try{n()}catch(r){te(e,t,r)}}var Pa=!1;function Jh(e,t){if(no=ys,e=Zu(),Zo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var l=0,a=-1,u=-1,c=0,p=0,m=e,g=null;t:for(;;){for(var j;m!==n||s!==0&&m.nodeType!==3||(a=l+s),m!==i||r!==0&&m.nodeType!==3||(u=l+r),m.nodeType===3&&(l+=m.nodeValue.length),(j=m.firstChild)!==null;)g=m,m=j;for(;;){if(m===e)break t;if(g===n&&++c===s&&(a=l),g===i&&++p===r&&(u=l),(j=m.nextSibling)!==null)break;m=g,g=m.parentNode}m=j}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ro={focusedElem:e,selectionRange:n},ys=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var _=x.memoizedProps,R=x.memoizedState,f=t.stateNode,d=f.getSnapshotBeforeUpdate(t.elementType===t.type?_:$e(t.type,_),R);f.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(L(163))}}catch(w){te(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return x=Pa,Pa=!1,x}function er(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&wo(t,n,i)}s=s.next}while(s!==r)}}function Qs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ko(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Yc(e){var t=e.alternate;t!==null&&(e.alternate=null,Yc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ge],delete t[pr],delete t[oo],delete t[Oh],delete t[Ah])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Xc(e){return e.tag===5||e.tag===3||e.tag===4}function Ma(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Xc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function jo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ws));else if(r!==4&&(e=e.child,e!==null))for(jo(e,t,n),e=e.sibling;e!==null;)jo(e,t,n),e=e.sibling}function No(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(No(e,t,n),e=e.sibling;e!==null;)No(e,t,n),e=e.sibling}var ue=null,Ue=!1;function ht(e,t,n){for(n=n.child;n!==null;)Gc(e,t,n),n=n.sibling}function Gc(e,t,n){if(Je&&typeof Je.onCommitFiberUnmount=="function")try{Je.onCommitFiberUnmount(Fs,n)}catch{}switch(n.tag){case 5:pe||cn(n,t);case 6:var r=ue,s=Ue;ue=null,ht(e,t,n),ue=r,Ue=s,ue!==null&&(Ue?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(Ue?(e=ue,n=n.stateNode,e.nodeType===8?vi(e.parentNode,n):e.nodeType===1&&vi(e,n),ur(e)):vi(ue,n.stateNode));break;case 4:r=ue,s=Ue,ue=n.stateNode.containerInfo,Ue=!0,ht(e,t,n),ue=r,Ue=s;break;case 0:case 11:case 14:case 15:if(!pe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,l=i.destroy;i=i.tag,l!==void 0&&(i&2||i&4)&&wo(n,t,l),s=s.next}while(s!==r)}ht(e,t,n);break;case 1:if(!pe&&(cn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){te(n,t,a)}ht(e,t,n);break;case 21:ht(e,t,n);break;case 22:n.mode&1?(pe=(r=pe)||n.memoizedState!==null,ht(e,t,n),pe=r):ht(e,t,n);break;default:ht(e,t,n)}}function Ra(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Gh),t.forEach(function(r){var s=lp.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Be(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,l=t,a=l;e:for(;a!==null;){switch(a.tag){case 5:ue=a.stateNode,Ue=!1;break e;case 3:ue=a.stateNode.containerInfo,Ue=!0;break e;case 4:ue=a.stateNode.containerInfo,Ue=!0;break e}a=a.return}if(ue===null)throw Error(L(160));Gc(i,l,s),ue=null,Ue=!1;var u=s.alternate;u!==null&&(u.return=null),s.return=null}catch(c){te(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Jc(t,e),t=t.sibling}function Jc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Be(t,e),Ye(e),r&4){try{er(3,e,e.return),Qs(3,e)}catch(_){te(e,e.return,_)}try{er(5,e,e.return)}catch(_){te(e,e.return,_)}}break;case 1:Be(t,e),Ye(e),r&512&&n!==null&&cn(n,n.return);break;case 5:if(Be(t,e),Ye(e),r&512&&n!==null&&cn(n,n.return),e.flags&32){var s=e.stateNode;try{ir(s,"")}catch(_){te(e,e.return,_)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,l=n!==null?n.memoizedProps:i,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&wu(s,i),Qi(a,l);var c=Qi(a,i);for(l=0;l<u.length;l+=2){var p=u[l],m=u[l+1];p==="style"?Cu(s,m):p==="dangerouslySetInnerHTML"?Nu(s,m):p==="children"?ir(s,m):Bo(s,p,m,c)}switch(a){case"input":$i(s,i);break;case"textarea":ku(s,i);break;case"select":var g=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var j=i.value;j!=null?hn(s,!!i.multiple,j,!1):g!==!!i.multiple&&(i.defaultValue!=null?hn(s,!!i.multiple,i.defaultValue,!0):hn(s,!!i.multiple,i.multiple?[]:"",!1))}s[pr]=i}catch(_){te(e,e.return,_)}}break;case 6:if(Be(t,e),Ye(e),r&4){if(e.stateNode===null)throw Error(L(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(_){te(e,e.return,_)}}break;case 3:if(Be(t,e),Ye(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ur(t.containerInfo)}catch(_){te(e,e.return,_)}break;case 4:Be(t,e),Ye(e);break;case 13:Be(t,e),Ye(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(xl=ne())),r&4&&Ra(e);break;case 22:if(p=n!==null&&n.memoizedState!==null,e.mode&1?(pe=(c=pe)||p,Be(t,e),pe=c):Be(t,e),Ye(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!p&&e.mode&1)for(z=e,p=e.child;p!==null;){for(m=z=p;z!==null;){switch(g=z,j=g.child,g.tag){case 0:case 11:case 14:case 15:er(4,g,g.return);break;case 1:cn(g,g.return);var x=g.stateNode;if(typeof x.componentWillUnmount=="function"){r=g,n=g.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(_){te(r,n,_)}}break;case 5:cn(g,g.return);break;case 22:if(g.memoizedState!==null){Aa(m);continue}}j!==null?(j.return=g,z=j):Aa(m)}p=p.sibling}e:for(p=null,m=e;;){if(m.tag===5){if(p===null){p=m;try{s=m.stateNode,c?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=m.stateNode,u=m.memoizedProps.style,l=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Su("display",l))}catch(_){te(e,e.return,_)}}}else if(m.tag===6){if(p===null)try{m.stateNode.nodeValue=c?"":m.memoizedProps}catch(_){te(e,e.return,_)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;p===m&&(p=null),m=m.return}p===m&&(p=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Be(t,e),Ye(e),r&4&&Ra(e);break;case 21:break;default:Be(t,e),Ye(e)}}function Ye(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Xc(n)){var r=n;break e}n=n.return}throw Error(L(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(ir(s,""),r.flags&=-33);var i=Ma(e);No(e,i,s);break;case 3:case 4:var l=r.stateNode.containerInfo,a=Ma(e);jo(e,a,l);break;default:throw Error(L(161))}}catch(u){te(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zh(e,t,n){z=e,Zc(e)}function Zc(e,t,n){for(var r=(e.mode&1)!==0;z!==null;){var s=z,i=s.child;if(s.tag===22&&r){var l=s.memoizedState!==null||Vr;if(!l){var a=s.alternate,u=a!==null&&a.memoizedState!==null||pe;a=Vr;var c=pe;if(Vr=l,(pe=u)&&!c)for(z=s;z!==null;)l=z,u=l.child,l.tag===22&&l.memoizedState!==null?za(s):u!==null?(u.return=l,z=u):za(s);for(;i!==null;)z=i,Zc(i),i=i.sibling;z=s,Vr=a,pe=c}Oa(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,z=i):Oa(e)}}function Oa(e){for(;z!==null;){var t=z;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:pe||Qs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!pe)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:$e(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&va(t,i,r);break;case 3:var l=t.updateQueue;if(l!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}va(t,l,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var p=c.memoizedState;if(p!==null){var m=p.dehydrated;m!==null&&ur(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(L(163))}pe||t.flags&512&&ko(t)}catch(g){te(t,t.return,g)}}if(t===e){z=null;break}if(n=t.sibling,n!==null){n.return=t.return,z=n;break}z=t.return}}function Aa(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var n=t.sibling;if(n!==null){n.return=t.return,z=n;break}z=t.return}}function za(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Qs(4,t)}catch(u){te(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(u){te(t,s,u)}}var i=t.return;try{ko(t)}catch(u){te(t,i,u)}break;case 5:var l=t.return;try{ko(t)}catch(u){te(t,l,u)}}}catch(u){te(t,t.return,u)}if(t===e){z=null;break}var a=t.sibling;if(a!==null){a.return=t.return,z=a;break}z=t.return}}var ep=Math.ceil,Ps=dt.ReactCurrentDispatcher,yl=dt.ReactCurrentOwner,Ie=dt.ReactCurrentBatchConfig,W=0,ae=null,re=null,ce=0,Ee=0,dn=Ot(0),oe=0,wr=null,Kt=0,qs=0,vl=0,tr=null,we=null,xl=0,Cn=1/0,nt=null,Ms=!1,So=null,Et=null,Wr=!1,xt=null,Rs=0,nr=0,Co=null,is=-1,os=0;function ye(){return W&6?ne():is!==-1?is:is=ne()}function _t(e){return e.mode&1?W&2&&ce!==0?ce&-ce:Ih.transition!==null?(os===0&&(os=Iu()),os):(e=H,e!==0||(e=window.event,e=e===void 0?16:Wu(e.type)),e):1}function Qe(e,t,n,r){if(50<nr)throw nr=0,Co=null,Error(L(185));Nr(e,n,r),(!(W&2)||e!==ae)&&(e===ae&&(!(W&2)&&(qs|=n),oe===4&&yt(e,ce)),Se(e,r),n===1&&W===0&&!(t.mode&1)&&(Cn=ne()+500,Vs&&At()))}function Se(e,t){var n=e.callbackNode;If(e,t);var r=gs(e,e===ae?ce:0);if(r===0)n!==null&&Hl(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Hl(n),t===1)e.tag===0?zh(Ia.bind(null,e)):uc(Ia.bind(null,e)),Mh(function(){!(W&6)&&At()}),n=null;else{switch(Fu(r)){case 1:n=Ho;break;case 4:n=Au;break;case 16:n=ms;break;case 536870912:n=zu;break;default:n=ms}n=ld(n,ed.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ed(e,t){if(is=-1,os=0,W&6)throw Error(L(327));var n=e.callbackNode;if(vn()&&e.callbackNode!==n)return null;var r=gs(e,e===ae?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Os(e,r);else{t=r;var s=W;W|=2;var i=nd();(ae!==e||ce!==t)&&(nt=null,Cn=ne()+500,Vt(e,t));do try{rp();break}catch(a){td(e,a)}while(1);sl(),Ps.current=i,W=s,re!==null?t=0:(ae=null,ce=0,t=oe)}if(t!==0){if(t===2&&(s=Gi(e),s!==0&&(r=s,t=Eo(e,s))),t===1)throw n=wr,Vt(e,0),yt(e,r),Se(e,ne()),n;if(t===6)yt(e,r);else{if(s=e.current.alternate,!(r&30)&&!tp(s)&&(t=Os(e,r),t===2&&(i=Gi(e),i!==0&&(r=i,t=Eo(e,i))),t===1))throw n=wr,Vt(e,0),yt(e,r),Se(e,ne()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(L(345));case 2:Dt(e,we,nt);break;case 3:if(yt(e,r),(r&130023424)===r&&(t=xl+500-ne(),10<t)){if(gs(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){ye(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=io(Dt.bind(null,e,we,nt),t);break}Dt(e,we,nt);break;case 4:if(yt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var l=31-He(r);i=1<<l,l=t[l],l>s&&(s=l),r&=~i}if(r=s,r=ne()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*ep(r/1960))-r,10<r){e.timeoutHandle=io(Dt.bind(null,e,we,nt),r);break}Dt(e,we,nt);break;case 5:Dt(e,we,nt);break;default:throw Error(L(329))}}}return Se(e,ne()),e.callbackNode===n?ed.bind(null,e):null}function Eo(e,t){var n=tr;return e.current.memoizedState.isDehydrated&&(Vt(e,t).flags|=256),e=Os(e,t),e!==2&&(t=we,we=n,t!==null&&_o(t)),e}function _o(e){we===null?we=e:we.push.apply(we,e)}function tp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!qe(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function yt(e,t){for(t&=~vl,t&=~qs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-He(t),r=1<<n;e[n]=-1,t&=~r}}function Ia(e){if(W&6)throw Error(L(327));vn();var t=gs(e,0);if(!(t&1))return Se(e,ne()),null;var n=Os(e,t);if(e.tag!==0&&n===2){var r=Gi(e);r!==0&&(t=r,n=Eo(e,r))}if(n===1)throw n=wr,Vt(e,0),yt(e,t),Se(e,ne()),n;if(n===6)throw Error(L(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Dt(e,we,nt),Se(e,ne()),null}function wl(e,t){var n=W;W|=1;try{return e(t)}finally{W=n,W===0&&(Cn=ne()+500,Vs&&At())}}function Yt(e){xt!==null&&xt.tag===0&&!(W&6)&&vn();var t=W;W|=1;var n=Ie.transition,r=H;try{if(Ie.transition=null,H=1,e)return e()}finally{H=r,Ie.transition=n,W=t,!(W&6)&&At()}}function kl(){Ee=dn.current,K(dn)}function Vt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ph(n)),re!==null)for(n=re.return;n!==null;){var r=n;switch(tl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ks();break;case 3:Nn(),K(je),K(me),cl();break;case 5:ul(r);break;case 4:Nn();break;case 13:K(G);break;case 19:K(G);break;case 10:il(r.type._context);break;case 22:case 23:kl()}n=n.return}if(ae=e,re=e=bt(e.current,null),ce=Ee=t,oe=0,wr=null,vl=qs=Kt=0,we=tr=null,$t!==null){for(t=0;t<$t.length;t++)if(n=$t[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var l=i.next;i.next=s,r.next=l}n.pending=r}$t=null}return e}function td(e,t){do{var n=re;try{if(sl(),ns.current=Ts,Ls){for(var r=J.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Ls=!1}if(qt=0,le=ie=J=null,Zn=!1,yr=0,yl.current=null,n===null||n.return===null){oe=1,wr=t,re=null;break}e:{var i=e,l=n.return,a=n,u=t;if(t=ce,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,p=a,m=p.tag;if(!(p.mode&1)&&(m===0||m===11||m===15)){var g=p.alternate;g?(p.updateQueue=g.updateQueue,p.memoizedState=g.memoizedState,p.lanes=g.lanes):(p.updateQueue=null,p.memoizedState=null)}var j=Sa(l);if(j!==null){j.flags&=-257,Ca(j,l,a,i,t),j.mode&1&&Na(i,c,t),t=j,u=c;var x=t.updateQueue;if(x===null){var _=new Set;_.add(u),t.updateQueue=_}else x.add(u);break e}else{if(!(t&1)){Na(i,c,t),jl();break e}u=Error(L(426))}}else if(Y&&a.mode&1){var R=Sa(l);if(R!==null){!(R.flags&65536)&&(R.flags|=256),Ca(R,l,a,i,t),nl(Sn(u,a));break e}}i=u=Sn(u,a),oe!==4&&(oe=2),tr===null?tr=[i]:tr.push(i),i=l;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var f=Fc(i,u,t);ya(i,f);break e;case 1:a=u;var d=i.type,h=i.stateNode;if(!(i.flags&128)&&(typeof d.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(Et===null||!Et.has(h)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=Dc(i,a,t);ya(i,w);break e}}i=i.return}while(i!==null)}sd(n)}catch(N){t=N,re===n&&n!==null&&(re=n=n.return);continue}break}while(1)}function nd(){var e=Ps.current;return Ps.current=Ts,e===null?Ts:e}function jl(){(oe===0||oe===3||oe===2)&&(oe=4),ae===null||!(Kt&268435455)&&!(qs&268435455)||yt(ae,ce)}function Os(e,t){var n=W;W|=2;var r=nd();(ae!==e||ce!==t)&&(nt=null,Vt(e,t));do try{np();break}catch(s){td(e,s)}while(1);if(sl(),W=n,Ps.current=r,re!==null)throw Error(L(261));return ae=null,ce=0,oe}function np(){for(;re!==null;)rd(re)}function rp(){for(;re!==null&&!bf();)rd(re)}function rd(e){var t=od(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?sd(e):re=t,yl.current=null}function sd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Xh(n,t),n!==null){n.flags&=32767,re=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{oe=6,re=null;return}}else if(n=Yh(n,t,Ee),n!==null){re=n;return}if(t=t.sibling,t!==null){re=t;return}re=t=e}while(t!==null);oe===0&&(oe=5)}function Dt(e,t,n){var r=H,s=Ie.transition;try{Ie.transition=null,H=1,sp(e,t,n,r)}finally{Ie.transition=s,H=r}return null}function sp(e,t,n,r){do vn();while(xt!==null);if(W&6)throw Error(L(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(L(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Ff(e,i),e===ae&&(re=ae=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Wr||(Wr=!0,ld(ms,function(){return vn(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Ie.transition,Ie.transition=null;var l=H;H=1;var a=W;W|=4,yl.current=null,Jh(e,n),Jc(n,e),Sh(ro),ys=!!no,ro=no=null,e.current=n,Zh(n),Lf(),W=a,H=l,Ie.transition=i}else e.current=n;if(Wr&&(Wr=!1,xt=e,Rs=s),i=e.pendingLanes,i===0&&(Et=null),Mf(n.stateNode),Se(e,ne()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Ms)throw Ms=!1,e=So,So=null,e;return Rs&1&&e.tag!==0&&vn(),i=e.pendingLanes,i&1?e===Co?nr++:(nr=0,Co=e):nr=0,At(),null}function vn(){if(xt!==null){var e=Fu(Rs),t=Ie.transition,n=H;try{if(Ie.transition=null,H=16>e?16:e,xt===null)var r=!1;else{if(e=xt,xt=null,Rs=0,W&6)throw Error(L(331));var s=W;for(W|=4,z=e.current;z!==null;){var i=z,l=i.child;if(z.flags&16){var a=i.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(z=c;z!==null;){var p=z;switch(p.tag){case 0:case 11:case 15:er(8,p,i)}var m=p.child;if(m!==null)m.return=p,z=m;else for(;z!==null;){p=z;var g=p.sibling,j=p.return;if(Yc(p),p===c){z=null;break}if(g!==null){g.return=j,z=g;break}z=j}}}var x=i.alternate;if(x!==null){var _=x.child;if(_!==null){x.child=null;do{var R=_.sibling;_.sibling=null,_=R}while(_!==null)}}z=i}}if(i.subtreeFlags&2064&&l!==null)l.return=i,z=l;else e:for(;z!==null;){if(i=z,i.flags&2048)switch(i.tag){case 0:case 11:case 15:er(9,i,i.return)}var f=i.sibling;if(f!==null){f.return=i.return,z=f;break e}z=i.return}}var d=e.current;for(z=d;z!==null;){l=z;var h=l.child;if(l.subtreeFlags&2064&&h!==null)h.return=l,z=h;else e:for(l=d;z!==null;){if(a=z,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Qs(9,a)}}catch(N){te(a,a.return,N)}if(a===l){z=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,z=w;break e}z=a.return}}if(W=s,At(),Je&&typeof Je.onPostCommitFiberRoot=="function")try{Je.onPostCommitFiberRoot(Fs,e)}catch{}r=!0}return r}finally{H=n,Ie.transition=t}}return!1}function Fa(e,t,n){t=Sn(n,t),t=Fc(e,t,1),e=Ct(e,t,1),t=ye(),e!==null&&(Nr(e,1,t),Se(e,t))}function te(e,t,n){if(e.tag===3)Fa(e,e,n);else for(;t!==null;){if(t.tag===3){Fa(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Et===null||!Et.has(r))){e=Sn(n,e),e=Dc(t,e,1),t=Ct(t,e,1),e=ye(),t!==null&&(Nr(t,1,e),Se(t,e));break}}t=t.return}}function ip(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ye(),e.pingedLanes|=e.suspendedLanes&n,ae===e&&(ce&n)===n&&(oe===4||oe===3&&(ce&130023424)===ce&&500>ne()-xl?Vt(e,0):vl|=n),Se(e,t)}function id(e,t){t===0&&(e.mode&1?(t=Or,Or<<=1,!(Or&130023424)&&(Or=4194304)):t=1);var n=ye();e=ut(e,t),e!==null&&(Nr(e,t,n),Se(e,n))}function op(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),id(e,n)}function lp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(L(314))}r!==null&&r.delete(t),id(e,n)}var od;od=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||je.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,Kh(e,t,n);ke=!!(e.flags&131072)}else ke=!1,Y&&t.flags&1048576&&cc(t,Ss,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ss(e,t),e=t.pendingProps;var s=wn(t,me.current);yn(t,n),s=fl(null,t,r,e,s,n);var i=hl();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ne(r)?(i=!0,js(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,ll(t),s.updater=Hs,t.stateNode=s,s._reactInternals=t,ho(t,r,e,n),t=go(null,t,r,!0,i,n)):(t.tag=0,Y&&i&&el(t),ge(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ss(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=up(r),e=$e(r,e),s){case 0:t=mo(null,t,r,e,n);break e;case 1:t=ba(null,t,r,e,n);break e;case 11:t=Ea(null,t,r,e,n);break e;case 14:t=_a(null,t,r,$e(r.type,e),n);break e}throw Error(L(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:$e(r,s),mo(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:$e(r,s),ba(e,t,r,s,n);case 3:e:{if(Vc(t),e===null)throw Error(L(387));r=t.pendingProps,i=t.memoizedState,s=i.element,gc(e,t),_s(t,r,null,n);var l=t.memoizedState;if(r=l.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:l.cache,pendingSuspenseBoundaries:l.pendingSuspenseBoundaries,transitions:l.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Sn(Error(L(423)),t),t=La(e,t,r,n,s);break e}else if(r!==s){s=Sn(Error(L(424)),t),t=La(e,t,r,n,s);break e}else for(be=St(t.stateNode.containerInfo.firstChild),Le=t,Y=!0,We=null,n=pc(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(kn(),r===s){t=ct(e,t,n);break e}ge(e,t,r,n)}t=t.child}return t;case 5:return yc(t),e===null&&uo(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,l=s.children,so(r,s)?l=null:i!==null&&so(r,i)&&(t.flags|=32),Uc(e,t),ge(e,t,l,n),t.child;case 6:return e===null&&uo(t),null;case 13:return Wc(e,t,n);case 4:return al(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=jn(t,null,r,n):ge(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:$e(r,s),Ea(e,t,r,s,n);case 7:return ge(e,t,t.pendingProps,n),t.child;case 8:return ge(e,t,t.pendingProps.children,n),t.child;case 12:return ge(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,l=s.value,Q(Cs,r._currentValue),r._currentValue=l,i!==null)if(qe(i.value,l)){if(i.children===s.children&&!je.current){t=ct(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){l=i.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(i.tag===1){u=ot(-1,n&-n),u.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var p=c.pending;p===null?u.next=u:(u.next=p.next,p.next=u),c.pending=u}}i.lanes|=n,u=i.alternate,u!==null&&(u.lanes|=n),co(i.return,n,t),a.lanes|=n;break}u=u.next}}else if(i.tag===10)l=i.type===t.type?null:i.child;else if(i.tag===18){if(l=i.return,l===null)throw Error(L(341));l.lanes|=n,a=l.alternate,a!==null&&(a.lanes|=n),co(l,n,t),l=i.sibling}else l=i.child;if(l!==null)l.return=i;else for(l=i;l!==null;){if(l===t){l=null;break}if(i=l.sibling,i!==null){i.return=l.return,l=i;break}l=l.return}i=l}ge(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,yn(t,n),s=Fe(s),r=r(s),t.flags|=1,ge(e,t,r,n),t.child;case 14:return r=t.type,s=$e(r,t.pendingProps),s=$e(r.type,s),_a(e,t,r,s,n);case 15:return Bc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:$e(r,s),ss(e,t),t.tag=1,Ne(r)?(e=!0,js(t)):e=!1,yn(t,n),Ic(t,r,s),ho(t,r,s,n),go(null,t,r,!0,e,n);case 19:return Hc(e,t,n);case 22:return $c(e,t,n)}throw Error(L(156,t.tag))};function ld(e,t){return Ou(e,t)}function ap(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new ap(e,t,n,r)}function Nl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function up(e){if(typeof e=="function")return Nl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Uo)return 11;if(e===Vo)return 14}return 2}function bt(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ls(e,t,n,r,s,i){var l=2;if(r=e,typeof e=="function")Nl(e)&&(l=1);else if(typeof e=="string")l=5;else e:switch(e){case en:return Wt(n.children,s,i,t);case $o:l=8,s|=8;break;case zi:return e=ze(12,n,t,s|2),e.elementType=zi,e.lanes=i,e;case Ii:return e=ze(13,n,t,s),e.elementType=Ii,e.lanes=i,e;case Fi:return e=ze(19,n,t,s),e.elementType=Fi,e.lanes=i,e;case yu:return Ks(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case mu:l=10;break e;case gu:l=9;break e;case Uo:l=11;break e;case Vo:l=14;break e;case pt:l=16,r=null;break e}throw Error(L(130,e==null?e:typeof e,""))}return t=ze(l,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function Wt(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Ks(e,t,n,r){return e=ze(22,e,r,t),e.elementType=yu,e.lanes=n,e.stateNode={isHidden:!1},e}function Ei(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function _i(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function cp(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=li(0),this.expirationTimes=li(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=li(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Sl(e,t,n,r,s,i,l,a,u){return e=new cp(e,t,n,a,u),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ze(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ll(i),e}function dp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Zt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function ad(e){if(!e)return Mt;e=e._reactInternals;e:{if(Gt(e)!==e||e.tag!==1)throw Error(L(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ne(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(L(171))}if(e.tag===1){var n=e.type;if(Ne(n))return ac(e,n,t)}return t}function ud(e,t,n,r,s,i,l,a,u){return e=Sl(n,r,!0,e,s,i,l,a,u),e.context=ad(null),n=e.current,r=ye(),s=_t(n),i=ot(r,s),i.callback=t??null,Ct(n,i,s),e.current.lanes=s,Nr(e,s,r),Se(e,r),e}function Ys(e,t,n,r){var s=t.current,i=ye(),l=_t(s);return n=ad(n),t.context===null?t.context=n:t.pendingContext=n,t=ot(i,l),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ct(s,t,l),e!==null&&(Qe(e,s,l,i),ts(e,s,l)),l}function As(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Da(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Cl(e,t){Da(e,t),(e=e.alternate)&&Da(e,t)}function fp(){return null}var cd=typeof reportError=="function"?reportError:function(e){console.error(e)};function El(e){this._internalRoot=e}Xs.prototype.render=El.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(L(409));Ys(e,t,null,null)};Xs.prototype.unmount=El.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yt(function(){Ys(null,e,null,null)}),t[at]=null}};function Xs(e){this._internalRoot=e}Xs.prototype.unstable_scheduleHydration=function(e){if(e){var t=$u();e={blockedOn:null,target:e,priority:t};for(var n=0;n<gt.length&&t!==0&&t<gt[n].priority;n++);gt.splice(n,0,e),n===0&&Vu(e)}};function _l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Gs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ba(){}function hp(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var c=As(l);i.call(c)}}var l=ud(t,r,e,0,null,!1,!1,"",Ba);return e._reactRootContainer=l,e[at]=l.current,fr(e.nodeType===8?e.parentNode:e),Yt(),l}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var c=As(u);a.call(c)}}var u=Sl(e,0,!1,null,null,!1,!1,"",Ba);return e._reactRootContainer=u,e[at]=u.current,fr(e.nodeType===8?e.parentNode:e),Yt(function(){Ys(t,u,n,r)}),u}function Js(e,t,n,r,s){var i=n._reactRootContainer;if(i){var l=i;if(typeof s=="function"){var a=s;s=function(){var u=As(l);a.call(u)}}Ys(t,l,e,s)}else l=hp(n,t,e,s,r);return As(l)}Du=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Wn(t.pendingLanes);n!==0&&(Qo(t,n|1),Se(t,ne()),!(W&6)&&(Cn=ne()+500,At()))}break;case 13:Yt(function(){var r=ut(e,1);if(r!==null){var s=ye();Qe(r,e,1,s)}}),Cl(e,1)}};qo=function(e){if(e.tag===13){var t=ut(e,134217728);if(t!==null){var n=ye();Qe(t,e,134217728,n)}Cl(e,134217728)}};Bu=function(e){if(e.tag===13){var t=_t(e),n=ut(e,t);if(n!==null){var r=ye();Qe(n,e,t,r)}Cl(e,t)}};$u=function(){return H};Uu=function(e,t){var n=H;try{return H=e,t()}finally{H=n}};Ki=function(e,t,n){switch(t){case"input":if($i(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Us(r);if(!s)throw Error(L(90));xu(r),$i(r,s)}}}break;case"textarea":ku(e,n);break;case"select":t=n.value,t!=null&&hn(e,!!n.multiple,t,!1)}};bu=wl;Lu=Yt;var pp={usingClientEntryPoint:!1,Events:[Cr,sn,Us,Eu,_u,wl]},Bn={findFiberByHostInstance:Bt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},mp={bundleType:Bn.bundleType,version:Bn.version,rendererPackageName:Bn.rendererPackageName,rendererConfig:Bn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:dt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Mu(e),e===null?null:e.stateNode},findFiberByHostInstance:Bn.findFiberByHostInstance||fp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Hr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Hr.isDisabled&&Hr.supportsFiber)try{Fs=Hr.inject(mp),Je=Hr}catch{}}Pe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=pp;Pe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!_l(t))throw Error(L(200));return dp(e,t,null,n)};Pe.createRoot=function(e,t){if(!_l(e))throw Error(L(299));var n=!1,r="",s=cd;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Sl(e,1,!1,null,null,n,!1,r,s),e[at]=t.current,fr(e.nodeType===8?e.parentNode:e),new El(t)};Pe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(L(188)):(e=Object.keys(e).join(","),Error(L(268,e)));return e=Mu(t),e=e===null?null:e.stateNode,e};Pe.flushSync=function(e){return Yt(e)};Pe.hydrate=function(e,t,n){if(!Gs(t))throw Error(L(200));return Js(null,e,t,!0,n)};Pe.hydrateRoot=function(e,t,n){if(!_l(e))throw Error(L(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",l=cd;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(l=n.onRecoverableError)),t=ud(t,null,e,1,n??null,s,!1,i,l),e[at]=t.current,fr(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Xs(t)};Pe.render=function(e,t,n){if(!Gs(t))throw Error(L(200));return Js(null,e,t,!1,n)};Pe.unmountComponentAtNode=function(e){if(!Gs(e))throw Error(L(40));return e._reactRootContainer?(Yt(function(){Js(null,null,e,!1,function(){e._reactRootContainer=null,e[at]=null})}),!0):!1};Pe.unstable_batchedUpdates=wl;Pe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Gs(n))throw Error(L(200));if(e==null||e._reactInternals===void 0)throw Error(L(38));return Js(e,t,n,!1,r)};Pe.version="18.3.1-next-f1338f8080-20240426";function dd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(dd)}catch(e){console.error(e)}}dd(),du.exports=Pe;var gp=du.exports,$a=gp;Oi.createRoot=$a.createRoot,Oi.hydrateRoot=$a.hydrateRoot;const tt=Object.create(null);tt.open="0";tt.close="1";tt.ping="2";tt.pong="3";tt.message="4";tt.upgrade="5";tt.noop="6";const as=Object.create(null);Object.keys(tt).forEach(e=>{as[tt[e]]=e});const bo={type:"error",data:"parser error"},fd=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",hd=typeof ArrayBuffer=="function",pd=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,bl=({type:e,data:t},n,r)=>fd&&t instanceof Blob?n?r(t):Ua(t,r):hd&&(t instanceof ArrayBuffer||pd(t))?n?r(t):Ua(new Blob([t]),r):r(tt[e]+(t||"")),Ua=(e,t)=>{const n=new FileReader;return n.onload=function(){const r=n.result.split(",")[1];t("b"+(r||""))},n.readAsDataURL(e)};function Va(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let bi;function yp(e,t){if(fd&&e.data instanceof Blob)return e.data.arrayBuffer().then(Va).then(t);if(hd&&(e.data instanceof ArrayBuffer||pd(e.data)))return t(Va(e.data));bl(e,!1,n=>{bi||(bi=new TextEncoder),t(bi.encode(n))})}const Wa="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Qn=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Wa.length;e++)Qn[Wa.charCodeAt(e)]=e;const vp=e=>{let t=e.length*.75,n=e.length,r,s=0,i,l,a,u;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const c=new ArrayBuffer(t),p=new Uint8Array(c);for(r=0;r<n;r+=4)i=Qn[e.charCodeAt(r)],l=Qn[e.charCodeAt(r+1)],a=Qn[e.charCodeAt(r+2)],u=Qn[e.charCodeAt(r+3)],p[s++]=i<<2|l>>4,p[s++]=(l&15)<<4|a>>2,p[s++]=(a&3)<<6|u&63;return c},xp=typeof ArrayBuffer=="function",Ll=(e,t)=>{if(typeof e!="string")return{type:"message",data:md(e,t)};const n=e.charAt(0);return n==="b"?{type:"message",data:wp(e.substring(1),t)}:as[n]?e.length>1?{type:as[n],data:e.substring(1)}:{type:as[n]}:bo},wp=(e,t)=>{if(xp){const n=vp(e);return md(n,t)}else return{base64:!0,data:e}},md=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},gd=String.fromCharCode(30),kp=(e,t)=>{const n=e.length,r=new Array(n);let s=0;e.forEach((i,l)=>{bl(i,!1,a=>{r[l]=a,++s===n&&t(r.join(gd))})})},jp=(e,t)=>{const n=e.split(gd),r=[];for(let s=0;s<n.length;s++){const i=Ll(n[s],t);if(r.push(i),i.type==="error")break}return r};function Np(){return new TransformStream({transform(e,t){yp(e,n=>{const r=n.length;let s;if(r<126)s=new Uint8Array(1),new DataView(s.buffer).setUint8(0,r);else if(r<65536){s=new Uint8Array(3);const i=new DataView(s.buffer);i.setUint8(0,126),i.setUint16(1,r)}else{s=new Uint8Array(9);const i=new DataView(s.buffer);i.setUint8(0,127),i.setBigUint64(1,BigInt(r))}e.data&&typeof e.data!="string"&&(s[0]|=128),t.enqueue(s),t.enqueue(n)})}})}let Li;function Qr(e){return e.reduce((t,n)=>t+n.length,0)}function qr(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let s=0;s<t;s++)n[s]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function Sp(e,t){Li||(Li=new TextDecoder);const n=[];let r=0,s=-1,i=!1;return new TransformStream({transform(l,a){for(n.push(l);;){if(r===0){if(Qr(n)<1)break;const u=qr(n,1);i=(u[0]&128)===128,s=u[0]&127,s<126?r=3:s===126?r=1:r=2}else if(r===1){if(Qr(n)<2)break;const u=qr(n,2);s=new DataView(u.buffer,u.byteOffset,u.length).getUint16(0),r=3}else if(r===2){if(Qr(n)<8)break;const u=qr(n,8),c=new DataView(u.buffer,u.byteOffset,u.length),p=c.getUint32(0);if(p>Math.pow(2,53-32)-1){a.enqueue(bo);break}s=p*Math.pow(2,32)+c.getUint32(4),r=3}else{if(Qr(n)<s)break;const u=qr(n,s);a.enqueue(Ll(i?u:Li.decode(u),t)),r=0}if(s===0||s>e){a.enqueue(bo);break}}}})}const yd=4;function se(e){if(e)return Cp(e)}function Cp(e){for(var t in se.prototype)e[t]=se.prototype[t];return e}se.prototype.on=se.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};se.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this};se.prototype.off=se.prototype.removeListener=se.prototype.removeAllListeners=se.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var n=this._callbacks["$"+e];if(!n)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var r,s=0;s<n.length;s++)if(r=n[s],r===t||r.fn===t){n.splice(s,1);break}return n.length===0&&delete this._callbacks["$"+e],this};se.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){n=n.slice(0);for(var r=0,s=n.length;r<s;++r)n[r].apply(this,t)}return this};se.prototype.emitReserved=se.prototype.emit;se.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};se.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Zs=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,n)=>n(t,0))(),Ae=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),Ep="arraybuffer";function vd(e,...t){return t.reduce((n,r)=>(e.hasOwnProperty(r)&&(n[r]=e[r]),n),{})}const _p=Ae.setTimeout,bp=Ae.clearTimeout;function ei(e,t){t.useNativeTimers?(e.setTimeoutFn=_p.bind(Ae),e.clearTimeoutFn=bp.bind(Ae)):(e.setTimeoutFn=Ae.setTimeout.bind(Ae),e.clearTimeoutFn=Ae.clearTimeout.bind(Ae))}const Lp=1.33;function Tp(e){return typeof e=="string"?Pp(e):Math.ceil((e.byteLength||e.size)*Lp)}function Pp(e){let t=0,n=0;for(let r=0,s=e.length;r<s;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}function xd(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function Mp(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}function Rp(e){let t={},n=e.split("&");for(let r=0,s=n.length;r<s;r++){let i=n[r].split("=");t[decodeURIComponent(i[0])]=decodeURIComponent(i[1])}return t}class Op extends Error{constructor(t,n,r){super(t),this.description=n,this.context=r,this.type="TransportError"}}class Tl extends se{constructor(t){super(),this.writable=!1,ei(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,n,r){return super.emitReserved("error",new Op(t,n,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const n=Ll(t,this.socket.binaryType);this.onPacket(n)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,n={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(n)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const n=Mp(t);return n.length?"?"+n:""}}class Ap extends Tl{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const n=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||n()})),this.writable||(r++,this.once("drain",function(){--r||n()}))}else n()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const n=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};jp(t,this.socket.binaryType).forEach(n),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,kp(t,n=>{this.doWrite(n,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",n=this.query||{};return this.opts.timestampRequests!==!1&&(n[this.opts.timestampParam]=xd()),!this.supportsBinary&&!n.sid&&(n.b64=1),this.createUri(t,n)}}let wd=!1;try{wd=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const zp=wd;function Ip(){}class Fp extends Ap{constructor(t){if(super(t),typeof location<"u"){const n=location.protocol==="https:";let r=location.port;r||(r=n?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,n){const r=this.request({method:"POST",data:t});r.on("success",n),r.on("error",(s,i)=>{this.onError("xhr post error",s,i)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(n,r)=>{this.onError("xhr poll error",n,r)}),this.pollXhr=t}}class et extends se{constructor(t,n,r){super(),this.createRequest=t,ei(this,r),this._opts=r,this._method=r.method||"GET",this._uri=n,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var t;const n=vd(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(n);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let s in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(s)&&r.setRequestHeader(s,this._opts.extraHeaders[s])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var s;r.readyState===3&&((s=this._opts.cookieJar)===null||s===void 0||s.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(s){this.setTimeoutFn(()=>{this._onError(s)},0);return}typeof document<"u"&&(this._index=et.requestsCount++,et.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=Ip,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete et.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}et.requestsCount=0;et.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Ha);else if(typeof addEventListener=="function"){const e="onpagehide"in Ae?"pagehide":"unload";addEventListener(e,Ha,!1)}}function Ha(){for(let e in et.requests)et.requests.hasOwnProperty(e)&&et.requests[e].abort()}const Dp=function(){const e=kd({xdomain:!1});return e&&e.responseType!==null}();class Bp extends Fp{constructor(t){super(t);const n=t&&t.forceBase64;this.supportsBinary=Dp&&!n}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new et(kd,this.uri(),t)}}function kd(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||zp))return new XMLHttpRequest}catch{}if(!t)try{return new Ae[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const jd=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class $p extends Tl{get name(){return"websocket"}doOpen(){const t=this.uri(),n=this.opts.protocols,r=jd?{}:vd(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,n,r)}catch(s){return this.emitReserved("error",s)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],s=n===t.length-1;bl(r,this.supportsBinary,i=>{try{this.doWrite(r,i)}catch{}s&&Zs(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",n=this.query||{};return this.opts.timestampRequests&&(n[this.opts.timestampParam]=xd()),this.supportsBinary||(n.b64=1),this.createUri(t,n)}}const Ti=Ae.WebSocket||Ae.MozWebSocket;class Up extends $p{createSocket(t,n,r){return jd?new Ti(t,n,r):n?new Ti(t,n):new Ti(t)}doWrite(t,n){this.ws.send(n)}}class Vp extends Tl{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const n=Sp(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(n).getReader(),s=Np();s.readable.pipeTo(t.writable),this._writer=s.writable.getWriter();const i=()=>{r.read().then(({done:a,value:u})=>{a||(this.onPacket(u),i())}).catch(a=>{})};i();const l={type:"open"};this.query.sid&&(l.data=`{"sid":"${this.query.sid}"}`),this._writer.write(l).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],s=n===t.length-1;this._writer.write(r).then(()=>{s&&Zs(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const Wp={websocket:Up,webtransport:Vp,polling:Bp},Hp=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Qp=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Lo(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");n!=-1&&r!=-1&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let s=Hp.exec(e||""),i={},l=14;for(;l--;)i[Qp[l]]=s[l]||"";return n!=-1&&r!=-1&&(i.source=t,i.host=i.host.substring(1,i.host.length-1).replace(/;/g,":"),i.authority=i.authority.replace("[","").replace("]","").replace(/;/g,":"),i.ipv6uri=!0),i.pathNames=qp(i,i.path),i.queryKey=Kp(i,i.query),i}function qp(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&r.splice(0,1),t.slice(-1)=="/"&&r.splice(r.length-1,1),r}function Kp(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,s,i){s&&(n[s]=i)}),n}const To=typeof addEventListener=="function"&&typeof removeEventListener=="function",us=[];To&&addEventListener("offline",()=>{us.forEach(e=>e())},!1);class Lt extends se{constructor(t,n){if(super(),this.binaryType=Ep,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(n=t,t=null),t){const r=Lo(t);n.hostname=r.host,n.secure=r.protocol==="https"||r.protocol==="wss",n.port=r.port,r.query&&(n.query=r.query)}else n.host&&(n.hostname=Lo(n.host).host);ei(this,n),this.secure=n.secure!=null?n.secure:typeof location<"u"&&location.protocol==="https:",n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.hostname=n.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=n.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},n.transports.forEach(r=>{const s=r.prototype.name;this.transports.push(s),this._transportsByName[s]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Rp(this.opts.query)),To&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},us.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const n=Object.assign({},this.opts.query);n.EIO=yd,n.transport=t,this.id&&(n.sid=this.id);const r=Object.assign({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&Lt.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const n=this.createTransport(t);n.open(),this.setTransport(n)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",n=>this._onClose("transport close",n))}onOpen(){this.readyState="open",Lt.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const n=new Error("server error");n.code=t.data,this._onError(n);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let n=1;for(let r=0;r<this.writeBuffer.length;r++){const s=this.writeBuffer[r].data;if(s&&(n+=Tp(s)),r>0&&n>this._maxPayload)return this.writeBuffer.slice(0,r);n+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,Zs(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,n,r){return this._sendPacket("message",t,n,r),this}send(t,n,r){return this._sendPacket("message",t,n,r),this}_sendPacket(t,n,r,s){if(typeof n=="function"&&(s=n,n=void 0),typeof r=="function"&&(s=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const i={type:t,data:n,options:r};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),s&&this.once("flush",s),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},n=()=>{this.off("upgrade",n),this.off("upgradeError",n),t()},r=()=>{this.once("upgrade",n),this.once("upgradeError",n)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(Lt.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,n){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),To&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=us.indexOf(this._offlineEventListener);r!==-1&&us.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,n),this.writeBuffer=[],this._prevBufferLen=0}}}Lt.protocol=yd;class Yp extends Lt{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let n=this.createTransport(t),r=!1;Lt.priorWebsocketSuccess=!1;const s=()=>{r||(n.send([{type:"ping",data:"probe"}]),n.once("packet",m=>{if(!r)if(m.type==="pong"&&m.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",n),!n)return;Lt.priorWebsocketSuccess=n.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(p(),this.setTransport(n),n.send([{type:"upgrade"}]),this.emitReserved("upgrade",n),n=null,this.upgrading=!1,this.flush())})}else{const g=new Error("probe error");g.transport=n.name,this.emitReserved("upgradeError",g)}}))};function i(){r||(r=!0,p(),n.close(),n=null)}const l=m=>{const g=new Error("probe error: "+m);g.transport=n.name,i(),this.emitReserved("upgradeError",g)};function a(){l("transport closed")}function u(){l("socket closed")}function c(m){n&&m.name!==n.name&&i()}const p=()=>{n.removeListener("open",s),n.removeListener("error",l),n.removeListener("close",a),this.off("close",u),this.off("upgrading",c)};n.once("open",s),n.once("error",l),n.once("close",a),this.once("close",u),this.once("upgrading",c),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{r||n.open()},200):n.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const n=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&n.push(t[r]);return n}}let Xp=class extends Yp{constructor(t,n={}){const r=typeof t=="object"?t:n;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(s=>Wp[s]).filter(s=>!!s)),super(t,r)}};function Gp(e,t="",n){let r=e;n=n||typeof location<"u"&&location,e==null&&(e=n.protocol+"//"+n.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=n.protocol+e:e=n.host+e),/^(https?|wss?):\/\//.test(e)||(typeof n<"u"?e=n.protocol+"//"+e:e="https://"+e),r=Lo(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const i=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+t,r.href=r.protocol+"://"+i+(n&&n.port===r.port?"":":"+r.port),r}const Jp=typeof ArrayBuffer=="function",Zp=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,Nd=Object.prototype.toString,em=typeof Blob=="function"||typeof Blob<"u"&&Nd.call(Blob)==="[object BlobConstructor]",tm=typeof File=="function"||typeof File<"u"&&Nd.call(File)==="[object FileConstructor]";function Pl(e){return Jp&&(e instanceof ArrayBuffer||Zp(e))||em&&e instanceof Blob||tm&&e instanceof File}function cs(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let n=0,r=e.length;n<r;n++)if(cs(e[n]))return!0;return!1}if(Pl(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return cs(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&cs(e[n]))return!0;return!1}function nm(e){const t=[],n=e.data,r=e;return r.data=Po(n,t),r.attachments=t.length,{packet:r,buffers:t}}function Po(e,t){if(!e)return e;if(Pl(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}else if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=Po(e[r],t);return n}else if(typeof e=="object"&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=Po(e[r],t));return n}return e}function rm(e,t){return e.data=Mo(e.data,t),delete e.attachments,e}function Mo(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=Mo(e[n],t);else if(typeof e=="object")for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=Mo(e[n],t));return e}const sm=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],im=5;var V;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(V||(V={}));class om{constructor(t){this.replacer=t}encode(t){return(t.type===V.EVENT||t.type===V.ACK)&&cs(t)?this.encodeAsBinary({type:t.type===V.EVENT?V.BINARY_EVENT:V.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let n=""+t.type;return(t.type===V.BINARY_EVENT||t.type===V.BINARY_ACK)&&(n+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(n+=t.nsp+","),t.id!=null&&(n+=t.id),t.data!=null&&(n+=JSON.stringify(t.data,this.replacer)),n}encodeAsBinary(t){const n=nm(t),r=this.encodeAsString(n.packet),s=n.buffers;return s.unshift(r),s}}function Qa(e){return Object.prototype.toString.call(e)==="[object Object]"}class Ml extends se{constructor(t){super(),this.reviver=t}add(t){let n;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");n=this.decodeString(t);const r=n.type===V.BINARY_EVENT;r||n.type===V.BINARY_ACK?(n.type=r?V.EVENT:V.ACK,this.reconstructor=new lm(n),n.attachments===0&&super.emitReserved("decoded",n)):super.emitReserved("decoded",n)}else if(Pl(t)||t.base64)if(this.reconstructor)n=this.reconstructor.takeBinaryData(t),n&&(this.reconstructor=null,super.emitReserved("decoded",n));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let n=0;const r={type:Number(t.charAt(0))};if(V[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===V.BINARY_EVENT||r.type===V.BINARY_ACK){const i=n+1;for(;t.charAt(++n)!=="-"&&n!=t.length;);const l=t.substring(i,n);if(l!=Number(l)||t.charAt(n)!=="-")throw new Error("Illegal attachments");r.attachments=Number(l)}if(t.charAt(n+1)==="/"){const i=n+1;for(;++n&&!(t.charAt(n)===","||n===t.length););r.nsp=t.substring(i,n)}else r.nsp="/";const s=t.charAt(n+1);if(s!==""&&Number(s)==s){const i=n+1;for(;++n;){const l=t.charAt(n);if(l==null||Number(l)!=l){--n;break}if(n===t.length)break}r.id=Number(t.substring(i,n+1))}if(t.charAt(++n)){const i=this.tryParse(t.substr(n));if(Ml.isPayloadValid(r.type,i))r.data=i;else throw new Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,n){switch(t){case V.CONNECT:return Qa(n);case V.DISCONNECT:return n===void 0;case V.CONNECT_ERROR:return typeof n=="string"||Qa(n);case V.EVENT:case V.BINARY_EVENT:return Array.isArray(n)&&(typeof n[0]=="number"||typeof n[0]=="string"&&sm.indexOf(n[0])===-1);case V.ACK:case V.BINARY_ACK:return Array.isArray(n)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class lm{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const n=rm(this.reconPack,this.buffers);return this.finishedReconstruction(),n}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const am=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Ml,Encoder:om,get PacketType(){return V},protocol:im},Symbol.toStringTag,{value:"Module"}));function Ve(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const um=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Sd extends se{constructor(t,n,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=n,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[Ve(t,"open",this.onopen.bind(this)),Ve(t,"packet",this.onpacket.bind(this)),Ve(t,"error",this.onerror.bind(this)),Ve(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...n){var r,s,i;if(um.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(n.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(n),this;const l={type:V.EVENT,data:n};if(l.options={},l.options.compress=this.flags.compress!==!1,typeof n[n.length-1]=="function"){const p=this.ids++,m=n.pop();this._registerAckCallback(p,m),l.id=p}const a=(s=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||s===void 0?void 0:s.writable,u=this.connected&&!(!((i=this.io.engine)===null||i===void 0)&&i._hasPingExpired());return this.flags.volatile&&!a||(u?(this.notifyOutgoingListeners(l),this.packet(l)):this.sendBuffer.push(l)),this.flags={},this}_registerAckCallback(t,n){var r;const s=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(s===void 0){this.acks[t]=n;return}const i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let a=0;a<this.sendBuffer.length;a++)this.sendBuffer[a].id===t&&this.sendBuffer.splice(a,1);n.call(this,new Error("operation has timed out"))},s),l=(...a)=>{this.io.clearTimeoutFn(i),n.apply(this,a)};l.withError=!0,this.acks[t]=l}emitWithAck(t,...n){return new Promise((r,s)=>{const i=(l,a)=>l?s(l):r(a);i.withError=!0,n.push(i),this.emit(t,...n)})}_addToQueue(t){let n;typeof t[t.length-1]=="function"&&(n=t.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((s,...i)=>r!==this._queue[0]?void 0:(s!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),n&&n(s)):(this._queue.shift(),n&&n(null,...i)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const n=this._queue[0];n.pending&&!t||(n.pending=!0,n.tryCount++,this.flags=n.flags,this.emit.apply(this,n.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:V.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,n){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,n),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(r=>String(r.id)===t)){const r=this.acks[t];delete this.acks[t],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case V.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case V.EVENT:case V.BINARY_EVENT:this.onevent(t);break;case V.ACK:case V.BINARY_ACK:this.onack(t);break;case V.DISCONNECT:this.ondisconnect();break;case V.CONNECT_ERROR:this.destroy();const r=new Error(t.data.message);r.data=t.data.data,this.emitReserved("connect_error",r);break}}onevent(t){const n=t.data||[];t.id!=null&&n.push(this.ack(t.id)),this.connected?this.emitEvent(n):this.receiveBuffer.push(Object.freeze(n))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const n=this._anyListeners.slice();for(const r of n)r.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const n=this;let r=!1;return function(...s){r||(r=!0,n.packet({type:V.ACK,id:t,data:s}))}}onack(t){const n=this.acks[t.id];typeof n=="function"&&(delete this.acks[t.id],n.withError&&t.data.unshift(null),n.apply(this,t.data))}onconnect(t,n){this.id=t,this.recovered=n&&this._pid===n,this._pid=n,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:V.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const n=this._anyListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const n=this._anyOutgoingListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const n=this._anyOutgoingListeners.slice();for(const r of n)r.apply(this,t.data)}}}function Ln(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Ln.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+n:e-n}return Math.min(e,this.max)|0};Ln.prototype.reset=function(){this.attempts=0};Ln.prototype.setMin=function(e){this.ms=e};Ln.prototype.setMax=function(e){this.max=e};Ln.prototype.setJitter=function(e){this.jitter=e};class Ro extends se{constructor(t,n){var r;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(n=t,t=void 0),n=n||{},n.path=n.path||"/socket.io",this.opts=n,ei(this,n),this.reconnection(n.reconnection!==!1),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor((r=n.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new Ln({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(n.timeout==null?2e4:n.timeout),this._readyState="closed",this.uri=t;const s=n.parser||am;this.encoder=new s.Encoder,this.decoder=new s.Decoder,this._autoConnect=n.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var n;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(n=this.backoff)===null||n===void 0||n.setMin(t),this)}randomizationFactor(t){var n;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(n=this.backoff)===null||n===void 0||n.setJitter(t),this)}reconnectionDelayMax(t){var n;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(n=this.backoff)===null||n===void 0||n.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Xp(this.uri,this.opts);const n=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const s=Ve(n,"open",function(){r.onopen(),t&&t()}),i=a=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",a),t?t(a):this.maybeReconnectOnOpen()},l=Ve(n,"error",i);if(this._timeout!==!1){const a=this._timeout,u=this.setTimeoutFn(()=>{s(),i(new Error("timeout")),n.close()},a);this.opts.autoUnref&&u.unref(),this.subs.push(()=>{this.clearTimeoutFn(u)})}return this.subs.push(s),this.subs.push(l),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(Ve(t,"ping",this.onping.bind(this)),Ve(t,"data",this.ondata.bind(this)),Ve(t,"error",this.onerror.bind(this)),Ve(t,"close",this.onclose.bind(this)),Ve(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(n){this.onclose("parse error",n)}}ondecoded(t){Zs(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,n){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new Sd(this,t,n),this.nsps[t]=r),r}_destroy(t){const n=Object.keys(this.nsps);for(const r of n)if(this.nsps[r].active)return;this._close()}_packet(t){const n=this.encoder.encode(t);for(let r=0;r<n.length;r++)this.engine.write(n[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,n){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,n),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const n=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(s=>{s?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",s)):t.onreconnect()}))},n);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const $n={};function ds(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const n=Gp(e,t.path||"/socket.io"),r=n.source,s=n.id,i=n.path,l=$n[s]&&i in $n[s].nsps,a=t.forceNew||t["force new connection"]||t.multiplex===!1||l;let u;return a?u=new Ro(r,t):($n[s]||($n[s]=new Ro(r,t)),u=$n[s]),n.query&&!t.query&&(t.query=n.queryKey),u.socket(n.path,t)}Object.assign(ds,{Manager:Ro,Socket:Sd,io:ds,connect:ds});const Cd=k.createContext(void 0);function cm({children:e}){const[t,n]=k.useState(null),[r,s]=k.useState(!1);k.useEffect(()=>{const c=ds(window.location.origin,{transports:["websocket","polling"],timeout:2e4,reconnection:!0,reconnectionAttempts:5,reconnectionDelay:1e3});return c.on("connect",()=>{console.log("🔌 Connected to NEXUS AI server"),s(!0)}),c.on("disconnect",p=>{console.log("🔌 Disconnected from server:",p),s(!1)}),c.on("connect_error",p=>{console.error("🔌 Connection error:",p),s(!1)}),c.on("reconnect",p=>{console.log(`🔌 Reconnected after ${p} attempts`),s(!0)}),c.on("reconnect_error",p=>{console.error("🔌 Reconnection error:",p)}),c.on("reconnect_failed",()=>{console.error("🔌 Failed to reconnect to server")}),n(c),()=>{c.disconnect()}},[]);const u={socket:t,connected:r,emit:(c,p)=>{t&&r?t.emit(c,p):console.warn(`Cannot emit ${c}: socket not connected`)},on:(c,p)=>{t&&t.on(c,p)},off:(c,p)=>{t&&(p?t.off(c,p):t.off(c))}};return o.jsx(Cd.Provider,{value:u,children:e})}function Tn(){const e=k.useContext(Cd);if(e===void 0)throw new Error("useSocket must be used within a SocketProvider");return e}const Ed=k.createContext(void 0);function dm({children:e}){const[t,n]=k.useState(()=>localStorage.getItem("nexus-theme")||"auto"),[r,s]=k.useState("dark");k.useEffect(()=>{const u=()=>{if(t==="auto"){const m=window.matchMedia("(prefers-color-scheme: dark)").matches;s(m?"dark":"light")}else s(t)};u();const c=window.matchMedia("(prefers-color-scheme: dark)"),p=()=>{t==="auto"&&u()};return c.addEventListener("change",p),()=>c.removeEventListener("change",p)},[t]),k.useEffect(()=>{const u=document.documentElement;r==="dark"?u.classList.add("dark"):u.classList.remove("dark"),localStorage.setItem("nexus-theme",t)},[t,r]);const i=u=>{n(u)},a={theme:t,actualTheme:r,setTheme:i,toggleTheme:()=>{i(t==="light"?"dark":t==="dark"?"auto":"light")}};return o.jsx(Ed.Provider,{value:a,children:e})}function _d(){const e=k.useContext(Ed);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e}const bd=k.createContext(void 0);function fm({children:e}){const{socket:t,connected:n,on:r,off:s,emit:i}=Tn(),[l,a]=k.useState([]),[u,c]=k.useState(null),[p,m]=k.useState([]),[g,j]=k.useState(!1);k.useEffect(()=>{if(!n)return;i("ai:get-providers");const f=w=>{if(a(w),!u&&w.length>0){const N=w.find(b=>b.isAvailable);N&&c(N.id)}},d=w=>{j(!1);const N={id:w.id,type:"assistant",content:w.content,timestamp:w.timestamp,metadata:{provider:w.provider,confidence:w.confidence,suggestions:w.suggestions}};m(b=>[...b,N])},h=w=>{j(!1);const N={id:Math.random().toString(36).substr(2,9),type:"system",content:`Error: ${w.error}`,timestamp:Date.now()};m(b=>[...b,N])};return r("ai:providers",f),r("ai:response",d),r("ai:error",h),()=>{s("ai:providers",f),s("ai:response",d),s("ai:error",h)}},[n,u,r,s,i]);const R={providers:l,selectedProvider:u,chatHistory:p,isLoading:g,sendMessage:async(f,d)=>{if(!n||g)return;const h={id:Math.random().toString(36).substr(2,9),type:"user",content:f,timestamp:Date.now()};m(w=>[...w,h]),j(!0),i("ai:query",{query:f,context:d||{currentFile:void 0,openFiles:[],projectType:"unknown",recentCommands:[]},provider:u,priority:"medium"})},clearHistory:()=>{m([])},setSelectedProvider:c};return o.jsx(bd.Provider,{value:R,children:e})}function ft(){const e=k.useContext(bd);if(e===void 0)throw new Error("useAI must be used within an AIContextProvider");return e}const Ld=k.createContext(void 0);function hm({children:e}){const{socket:t,connected:n,on:r,off:s,emit:i}=Tn(),[l,a]=k.useState(null),[u,c]=k.useState([]),[p,m]=k.useState(null),[g,j]=k.useState([]),[x,_]=k.useState(!1);k.useEffect(()=>{if(!n)return;const w=C=>{a(C),_(!0)},N=C=>{c(C)},b=C=>{m(C)},v=C=>{j(C)},y=C=>{console.error("System error:",C.error)};return r("system:metrics",w),r("system:processes",N),r("system:info",b),r("system:alerts",v),r("system:error",y),i("system:get-processes"),i("system:get-info"),()=>{s("system:metrics",w),s("system:processes",N),s("system:info",b),s("system:alerts",v),s("system:error",y)}},[n,r,s,i]);const h={metrics:l,processes:u,systemInfo:p,alerts:g,isMonitoring:x,refreshMetrics:()=>{},refreshProcesses:()=>{n&&i("system:get-processes")},refreshSystemInfo:()=>{n&&i("system:get-info")}};return o.jsx(Ld.Provider,{value:h,children:e})}function Pn(){const e=k.useContext(Ld);if(e===void 0)throw new Error("useSystem must be used within a SystemProvider");return e}const Td=k.createContext(void 0);function pm({children:e}){const{socket:t,connected:n,on:r,off:s,emit:i}=Tn(),[l,a]=k.useState([]),[u,c]=k.useState(null);k.useEffect(()=>{if(!n)return;const d=C=>{if(a(C),!u&&C.length>0){const E=C.find(S=>S.isActive);E&&c(E.id)}},h=C=>{a(E=>[...E,C]),l.length===0&&c(C.id)},w=C=>{if(a(E=>E.filter(S=>S.id!==C)),u===C){const E=l.filter(S=>S.id!==C);c(E.length>0?E[0].id:null)}},N=C=>{c(C),a(E=>E.map(S=>({...S,isActive:S.id===C})))},b=C=>{a(E=>E.map(S=>{if(S.id===C.sessionId){const O=S.blocks.map(B=>B.id===C.blockId&&B.status==="running"?{...B,output:B.output+C.data}:B);return{...S,blocks:O,lastActivity:Date.now()}}return S}))},v=C=>{a(E=>E.map(S=>S.id===C.sessionId?{...S,blocks:[...S.blocks.filter(O=>O.id!==C.block.id),C.block],lastActivity:Date.now()}:S))},y=C=>{console.error("Terminal error:",C.error)};return r("terminal:sessions",d),r("terminal:session-created",h),r("terminal:session-closed",w),r("terminal:active-changed",N),r("terminal:output",b),r("terminal:command-executed",v),r("terminal:error",y),()=>{s("terminal:sessions",d),s("terminal:session-created",h),s("terminal:session-closed",w),s("terminal:active-changed",N),s("terminal:output",b),s("terminal:command-executed",v),s("terminal:error",y)}},[n,u,l.length,r,s,i]);const f={sessions:l,activeSessionId:u,createSession:(d,h)=>{n&&i("terminal:create",{name:d,workingDirectory:h})},closeSession:d=>{n&&i("terminal:close",{sessionId:d})},setActiveSession:d=>{n&&i("terminal:set-active",{sessionId:d})},executeCommand:(d,h)=>{n&&i("terminal:execute",{sessionId:d,command:h})},writeToTerminal:(d,h)=>{n&&i("terminal:input",{sessionId:d,input:h})},resizeTerminal:(d,h,w)=>{n&&i("terminal:resize",{sessionId:d,cols:h,rows:w})},getActiveSession:()=>l.find(d=>d.id===u)||null};return o.jsx(Td.Provider,{value:f,children:e})}function Mn(){const e=k.useContext(Td);if(e===void 0)throw new Error("useTerminal must be used within a TerminalProvider");return e}const Pd=k.createContext(void 0);function mm({children:e}){const{socket:t,connected:n,on:r,off:s,emit:i}=Tn(),[l,a]=k.useState(null),[u,c]=k.useState(null),[p,m]=k.useState(null),[g,j]=k.useState([]),[x,_]=k.useState([]),[R,f]=k.useState({});k.useEffect(()=>{if(!n)return;const T=M=>{console.log(`File ${M.type}: ${M.path}`),(M.type==="added"||M.type==="deleted")&&S(),M.type==="modified"&&g.includes(M.path)&&console.log(`Open file modified externally: ${M.path}`)},A=M=>{f(F=>new Map(F.set(M.path,M.content)))},$=M=>{console.log(`File written: ${M.path}`)},P=M=>{_(F=>[...F.filter(ee=>ee!==M.path),M.path])},I=M=>{_(F=>F.filter(ee=>ee!==M.path))},D=M=>{console.error("FileSystem error:",M.error)};return r("filesystem:changed",T),r("filesystem:file-content",A),r("filesystem:file-written",$),r("filesystem:watching",P),r("filesystem:unwatched",I),r("filesystem:error",D),S(),O(),()=>{s("filesystem:changed",T),s("filesystem:file-content",A),s("filesystem:file-written",$),s("filesystem:watching",P),s("filesystem:unwatched",I),s("filesystem:error",D)}},[n,r,s,i]);const d=async T=>new Promise((A,$)=>{if(!n){$(new Error("Not connected to server"));return}const P=R[T];if(P!==void 0){A(P);return}const I=M=>{M.path===T&&(s("filesystem:file-content",I),A(M.content))},D=M=>{s("filesystem:file-content",I),s("filesystem:error",D),$(new Error(M.error))};r("filesystem:file-content",I),r("filesystem:error",D),i("filesystem:read",{path:T}),setTimeout(()=>{s("filesystem:file-content",I),s("filesystem:error",D),$(new Error("File read timeout"))},1e4)}),h=(T,A)=>{n&&(i("filesystem:write",{path:T,content:A}),f($=>({...$,[T]:A})))},w=T=>{n&&console.log("Create directory:",T)},N=T=>{n&&console.log("Delete file:",T)},b=async T=>{if(g.includes(T)||j(A=>[...A,T]),m(T),!R[T])try{const A=await d(T);f($=>({...$,[T]:A}))}catch(A){console.error("Failed to load file content:",A),f($=>({...$,[T]:""}))}},v=(T,A)=>{f($=>({...$,[T]:A}))},y=T=>{if(j(A=>A.filter($=>$!==T)),p===T){const A=g.filter($=>$!==T);m(A.length>0?A[A.length-1]:null)}f(A=>{const{[T]:$,...P}=A;return P})},C=T=>{n&&!x.includes(T)&&i("filesystem:watch",{path:T})},E=T=>{n&&x.includes(T)&&i("filesystem:unwatch",{path:T})},S=T=>{n&&fetch(`/api/filesystem/tree?path=${encodeURIComponent(T||process.cwd())}`).then(A=>A.json()).then(A=>a(A)).catch(A=>console.error("Failed to refresh file tree:",A))},O=T=>{n&&fetch(`/api/filesystem/project?path=${encodeURIComponent(T||process.cwd())}`).then(A=>A.json()).then(A=>c(A)).catch(A=>console.error("Failed to analyze project:",A))},X={fileTree:l,currentProject:u,selectedFile:p,openFiles:g,watchedDirectories:x,fileContents:R,readFile:d,writeFile:h,createDirectory:w,deleteFile:N,openFile:b,closeFile:y,setSelectedFile:m,watchDirectory:C,unwatchDirectory:E,refreshFileTree:S,analyzeProject:O,searchFiles:async(T,A)=>{if(!n)return[];try{const $=new URLSearchParams({query:T,path:process.cwd()});return A&&A.length>0&&$.append("extensions",A.join(",")),await(await fetch(`/api/filesystem/search?${$}`)).json()}catch($){return console.error("Search failed:",$),[]}},updateFileContent:v};return o.jsx(Pd.Provider,{value:X,children:e})}function zt(){const e=k.useContext(Pd);if(e===void 0)throw new Error("useFileSystem must be used within a FileSystemProvider");return e}function gm({onToggleSidebar:e,sidebarCollapsed:t}){const{connected:n}=Tn(),{theme:r,toggleTheme:s}=_d(),{metrics:i}=Pn(),{providers:l,selectedProvider:a}=ft(),c=(()=>n?{text:"Connected",color:"text-green-500",dot:"bg-green-500"}:{text:"Disconnected",color:"text-red-500",dot:"bg-red-500"})(),p=l.find(m=>m.id===a);return o.jsx("header",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-3",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("button",{onClick:e,className:"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors",title:t?"Expand sidebar":"Collapse sidebar",children:o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-nexus-500 to-nexus-700 rounded-lg flex items-center justify-center",children:o.jsx("span",{className:"text-white font-bold text-sm",children:"N"})}),o.jsxs("div",{children:[o.jsx("h1",{className:"text-lg font-bold text-gray-900 dark:text-white",children:"NEXUS AI"}),o.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Unified Development Environment"})]})]}),o.jsxs("div",{className:"hidden md:flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400",children:[o.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 1v6"})]}),o.jsx("span",{children:"NEXUS-AI"})]})]}),o.jsxs("div",{className:"hidden lg:flex items-center space-x-2",children:[o.jsxs("button",{className:"btn btn-sm btn-secondary",children:[o.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),"Search"]}),o.jsxs("button",{className:"btn btn-sm btn-secondary",children:[o.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 9l3 3-3 3m5 0h3"})}),"Command"]})]}),o.jsxs("div",{className:"flex items-center space-x-4",children:[i&&o.jsxs("div",{className:"hidden md:flex items-center space-x-4 text-sm",children:[o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"CPU:"}),o.jsxs("span",{className:`font-mono ${i.cpu.usage>80?"text-red-500":i.cpu.usage>60?"text-yellow-500":"text-green-500"}`,children:[i.cpu.usage,"%"]})]}),o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"RAM:"}),o.jsxs("span",{className:`font-mono ${i.memory.percentage>80?"text-red-500":i.memory.percentage>60?"text-yellow-500":"text-green-500"}`,children:[i.memory.percentage,"%"]})]})]}),p&&o.jsx("div",{className:"hidden md:flex items-center space-x-2 text-sm",children:o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),o.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:p.name})]})}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:`w-2 h-2 rounded-full ${c.dot}`}),o.jsx("span",{className:`text-sm ${c.color}`,children:c.text})]}),o.jsx("button",{onClick:s,className:"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors",title:`Switch to ${r==="light"?"dark":r==="dark"?"auto":"light"} theme`,children:r==="light"?o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):r==="dark"?o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})}):o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})}),o.jsx("button",{className:"p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors",children:o.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})})]})]})})}function Md({node:e,level:t,onFileSelect:n,selectedFile:r,expandedDirs:s,onToggleDir:i}){const l=s.has(e.path),a=r===e.path,u=(p,m)=>{var x;if(m)return l?"📂":"📁";const g=(x=p.split(".").pop())==null?void 0:x.toLowerCase();return{ts:"🔷",tsx:"⚛️",js:"🟨",jsx:"⚛️",py:"🐍",java:"☕",cpp:"⚙️",c:"⚙️",cs:"🔷",php:"🐘",rb:"💎",go:"🐹",rs:"🦀",html:"🌐",css:"🎨",scss:"🎨",json:"📋",xml:"📄",yaml:"📄",yml:"📄",md:"📝",sql:"🗃️",sh:"🐚",bash:"🐚",dockerfile:"🐳",gitignore:"🚫",env:"🔐"}[g||""]||"📄"},c=()=>{e.type==="directory"?i(e.path):n(e.path)};return o.jsxs("div",{children:[o.jsxs("div",{className:`flex items-center space-x-2 px-2 py-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-700 ${a?"bg-blue-100 dark:bg-blue-900/30":""}`,style:{paddingLeft:`${t*16+8}px`},onClick:c,children:[o.jsx("span",{className:"text-sm",children:u(e.name,e.type==="directory")}),o.jsx("span",{className:`text-sm truncate ${a?"text-blue-600 dark:text-blue-400":"text-gray-700 dark:text-gray-300"}`,children:e.name}),e.type==="file"&&e.size&&o.jsx("span",{className:"text-xs text-gray-400 ml-auto",children:ym(e.size)})]}),e.type==="directory"&&l&&e.children&&o.jsx("div",{children:e.children.map(p=>o.jsx(Md,{node:p,level:t+1,onFileSelect:n,selectedFile:r,expandedDirs:s,onToggleDir:i},p.path))})]})}function ym(e){if(e===0)return"0 B";const t=1024,n=["B","KB","MB","GB"],r=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,r)).toFixed(1))+" "+n[r]}function vm({className:e=""}){const{fileTree:t,selectedFile:n,openFile:r,refreshFileTree:s}=zt(),[i,l]=k.useState(new Set),[a,u]=k.useState("");k.useEffect(()=>{s()},[]);const c=g=>{l(j=>{const x=new Set(j);return x.has(g)?x.delete(g):x.add(g),x})},p=g=>{r(g)},m=wt.useMemo(()=>{if(!t||!a)return t;const g=j=>{var R;const x=j.name.toLowerCase().includes(a.toLowerCase());if(j.type==="file")return x?j:null;const _=((R=j.children)==null?void 0:R.map(g).filter(Boolean))||[];return x||_.length>0?{...j,children:_}:null};return g(t)},[t,a]);return o.jsxs("div",{className:`flex flex-col h-full ${e}`,children:[o.jsxs("div",{className:"p-3 border-b border-gray-200 dark:border-dark-700",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("h3",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:"Explorer"}),o.jsx("button",{onClick:()=>s(),className:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Refresh",children:o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]}),o.jsxs("div",{className:"relative",children:[o.jsx("input",{type:"text",placeholder:"Search files...",value:a,onChange:g=>u(g.target.value),className:"w-full px-3 py-1 text-sm bg-gray-100 dark:bg-dark-700 border border-gray-200 dark:border-dark-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"}),o.jsx("svg",{className:"absolute right-2 top-1.5 w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]})]}),o.jsx("div",{className:"flex-1 overflow-y-auto",children:m?o.jsx(Md,{node:m,level:0,onFileSelect:p,selectedFile:n,expandedDirs:i,onToggleDir:c}):o.jsxs("div",{className:"p-4 text-center text-gray-500 dark:text-gray-400",children:[o.jsx("svg",{className:"w-12 h-12 mx-auto mb-2 text-gray-300 dark:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"})}),o.jsx("p",{className:"text-sm",children:"No files found"}),o.jsx("button",{onClick:()=>s(),className:"mt-2 text-xs text-blue-500 hover:text-blue-600",children:"Refresh Explorer"})]})})]})}function Rd(){const{selectedFile:e,openFiles:t,fileContents:n}=zt(),{sendMessage:r}=ft(),[s,i]=k.useState([]),[l,a]=k.useState(null),[u,c]=k.useState(!1),[p,m]=k.useState("all");k.useEffect(()=>{e&&n[e]&&g()},[e,n]);const g=async()=>{if(!(!e||!n[e])){c(!0);try{const f=[{id:"1",type:"bug",severity:"high",title:"Potential Null Pointer Exception",description:"Variable may be null before use",file:e,line:42,suggestion:"Add null check before accessing the variable",confidence:.85},{id:"2",type:"performance",severity:"medium",title:"Inefficient Loop",description:"Nested loop with O(n²) complexity",file:e,line:78,suggestion:"Consider using a Map or Set for faster lookups",confidence:.75},{id:"3",type:"security",severity:"critical",title:"SQL Injection Vulnerability",description:"User input directly concatenated to SQL query",file:e,line:156,suggestion:"Use parameterized queries or prepared statements",confidence:.95},{id:"4",type:"style",severity:"low",title:"Inconsistent Naming",description:"Variable name does not follow camelCase convention",file:e,line:23,suggestion:"Rename variable to follow camelCase convention",confidence:.9}],d={linesOfCode:n[e].split(`
`).length,complexity:Math.floor(Math.random()*20)+5,maintainability:Math.floor(Math.random()*40)+60,testCoverage:Math.floor(Math.random()*50)+30,duplicateCode:Math.floor(Math.random()*15)+2,technicalDebt:Math.floor(Math.random()*30)+10};i(f),a(d)}catch(f){console.error("Code analysis failed:",f)}finally{c(!1)}}},j=async f=>{const d=`Help me fix this ${f.type} issue: ${f.title}. ${f.description}. Suggested fix: ${f.suggestion}. File: ${f.file}${f.line?`, Line: ${f.line}`:""}`;await r(d,{currentFile:e,openFiles:t,projectType:"code-analysis",recentCommands:[],systemState:null})},x=f=>{switch(f){case"bug":return"🐛";case"performance":return"⚡";case"security":return"🔒";case"style":return"🎨";case"suggestion":return"💡";default:return"📝"}},_=f=>{switch(f){case"critical":return"text-red-600 bg-red-100 dark:bg-red-900/20";case"high":return"text-orange-600 bg-orange-100 dark:bg-orange-900/20";case"medium":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20";case"low":return"text-blue-600 bg-blue-100 dark:bg-blue-900/20";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/20"}},R=p==="all"?s:s.filter(f=>f.type===p);return e?o.jsxs("div",{className:"flex-1 flex flex-col",children:[o.jsx("div",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 p-4",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Code Analysis"}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.split("/").pop()})]}),o.jsx("button",{onClick:g,disabled:u,className:"btn btn-primary",children:u?o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),o.jsx("span",{children:"Analyzing..."})]}):"🔍 Analyze Code"})]})}),l&&o.jsx("div",{className:"bg-gray-50 dark:bg-dark-900 border-b border-gray-200 dark:border-dark-700 p-4",children:o.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-blue-600 dark:text-blue-400",children:l.linesOfCode}),o.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Lines of Code"})]}),o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-purple-600 dark:text-purple-400",children:l.complexity}),o.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Complexity"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-2xl font-bold text-green-600 dark:text-green-400",children:[l.maintainability,"%"]}),o.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Maintainability"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-2xl font-bold text-yellow-600 dark:text-yellow-400",children:[l.testCoverage,"%"]}),o.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Test Coverage"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-2xl font-bold text-red-600 dark:text-red-400",children:[l.duplicateCode,"%"]}),o.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Duplicate Code"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-2xl font-bold text-orange-600 dark:text-orange-400",children:[l.technicalDebt,"h"]}),o.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Technical Debt"})]})]})}),o.jsx("div",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-2",children:o.jsx("div",{className:"flex space-x-1",children:["all","bug","performance","security","style","suggestion"].map(f=>o.jsxs("button",{onClick:()=>m(f),className:`px-3 py-1 text-sm rounded-md transition-colors ${p===f?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-700"}`,children:[x(f)," ",f.charAt(0).toUpperCase()+f.slice(1),f!=="all"&&o.jsxs("span",{className:"ml-1 text-xs",children:["(",s.filter(d=>d.type===f).length,")"]})]},f))})}),o.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:R.length===0?o.jsxs("div",{className:"text-center py-8",children:[o.jsx("div",{className:"text-4xl mb-2",children:"✨"}),o.jsx("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:p==="all"?"No Issues Found":`No ${p} Issues`}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Your code looks great! Keep up the good work."})]}):o.jsx("div",{className:"space-y-4",children:R.map(f=>o.jsx("div",{className:"bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-700 rounded-lg p-4",children:o.jsxs("div",{className:"flex items-start justify-between",children:[o.jsxs("div",{className:"flex-1",children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx("span",{className:"text-lg",children:x(f.type)}),o.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:f.title}),o.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${_(f.severity)}`,children:f.severity}),o.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:[Math.round(f.confidence*100),"% confidence"]})]}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2",children:f.description}),f.line&&o.jsxs("p",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:["Line ",f.line]}),o.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded p-2",children:o.jsxs("p",{className:"text-sm text-blue-800 dark:text-blue-200",children:["💡 ",o.jsx("strong",{children:"Suggestion:"})," ",f.suggestion]})})]}),o.jsx("button",{onClick:()=>j(f),className:"ml-4 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"🤖 AI Fix"})]})},f.id))})})]}):o.jsx("div",{className:"flex-1 flex items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("svg",{className:"w-16 h-16 mx-auto mb-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Code Analysis"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Select a file to analyze code quality and get intelligent insights"})]})})}function Od(){const{sendMessage:e}=ft();Mn();const{openFile:t,selectedFile:n}=zt(),[r,s]=k.useState([]),[i,l]=k.useState([]),[a,u]=k.useState(!1),[c,p]=k.useState([]),[m,g]=k.useState(null),[j,x]=k.useState(!1);k.useEffect(()=>{_(),R()},[]);const _=()=>{const y=[{id:"setup-dev",name:"Development Setup",description:"Set up development environment for a new project",steps:[{id:"1",type:"command",description:"Install dependencies",action:"npm install",status:"pending"},{id:"2",type:"command",description:"Start development server",action:"npm run dev",status:"pending"},{id:"3",type:"file_operation",description:"Open main file",action:"open_file",parameters:{file:"src/App.tsx"},status:"pending"}],frequency:15,lastUsed:new Date,isAutomated:!1},{id:"git-workflow",name:"Git Commit Workflow",description:"Standard git workflow for committing changes",steps:[{id:"1",type:"command",description:"Check git status",action:"git status",status:"pending"},{id:"2",type:"command",description:"Add all changes",action:"git add .",status:"pending"},{id:"3",type:"ai_query",description:"Generate commit message",action:"Generate a commit message for the current changes",status:"pending"}],frequency:42,lastUsed:new Date(Date.now()-864e5),isAutomated:!0}];s(y)},R=()=>{l([{id:"test-workflow",name:"Testing Workflow",description:"You often run tests after making changes",confidence:.85,steps:["Save file","Run tests","Check results"],pattern:["file_save","npm test","check_output"]},{id:"deploy-workflow",name:"Deployment Workflow",description:"Automated deployment process",confidence:.72,steps:["Build project","Run tests","Deploy to staging"],pattern:["npm run build","npm test","deploy"]}])},f=()=>{u(!0),p([])},d=()=>{u(!1),c.length>0&&h()},h=()=>{const y=prompt("Enter a name for this workflow:");if(!y)return;const C={id:Date.now().toString(),name:y,description:`Recorded workflow with ${c.length} steps`,steps:c.map((E,S)=>({id:(S+1).toString(),type:E.startsWith("npm")||E.startsWith("git")?"command":"ai_query",description:E,action:E,status:"pending"})),frequency:1,lastUsed:new Date,isAutomated:!1};s(E=>[...E,C]),p([])},w=async y=>{var E;const C=r.find(S=>S.id===y);if(C){x(!0),g(y);try{for(const S of C.steps){switch(s(O=>O.map(B=>B.id===y?{...B,steps:B.steps.map(X=>X.id===S.id?{...X,status:"running"}:X)}:B)),S.type){case"command":await new Promise(O=>setTimeout(O,1e3));break;case"file_operation":S.action==="open_file"&&((E=S.parameters)!=null&&E.file)&&t(S.parameters.file);break;case"ai_query":await e(S.action,{currentFile:n,openFiles:[],projectType:"workflow-automation",recentCommands:[],systemState:null});break;case"wait":await new Promise(O=>{var B;return setTimeout(O,((B=S.parameters)==null?void 0:B.duration)||1e3)});break}s(O=>O.map(B=>B.id===y?{...B,steps:B.steps.map(X=>X.id===S.id?{...X,status:"completed"}:X)}:B))}s(S=>S.map(O=>O.id===y?{...O,lastUsed:new Date,frequency:O.frequency+1}:O))}catch(S){console.error("Workflow execution failed:",S)}finally{x(!1),g(null)}}},N=y=>{const C={id:y.id,name:y.name,description:y.description,steps:y.steps.map((E,S)=>({id:(S+1).toString(),type:"command",description:E,action:y.pattern[S]||E,status:"pending"})),frequency:0,lastUsed:new Date,isAutomated:!1};s(E=>[...E,C]),l(E=>E.filter(S=>S.id!==y.id))},b=y=>{switch(y){case"command":return"⚡";case"file_operation":return"📁";case"ai_query":return"🤖";case"wait":return"⏱️";default:return"📝"}},v=y=>{switch(y){case"completed":return"text-green-600 bg-green-100 dark:bg-green-900/20";case"running":return"text-blue-600 bg-blue-100 dark:bg-blue-900/20";case"failed":return"text-red-600 bg-red-100 dark:bg-red-900/20";default:return"text-gray-600 bg-gray-100 dark:bg-gray-900/20"}};return o.jsxs("div",{className:"flex-1 flex flex-col",children:[o.jsxs("div",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 p-4",children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Workflow Automation"}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Automate repetitive tasks and learn from your patterns"})]}),o.jsx("div",{className:"flex space-x-2",children:o.jsx("button",{onClick:a?d:f,className:`btn ${a?"btn-danger":"btn-primary"}`,children:a?o.jsxs(o.Fragment,{children:[o.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"}),"Stop Recording"]}):"🎬 Record Workflow"})})]}),a&&o.jsxs("div",{className:"mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),o.jsxs("span",{className:"text-sm text-red-700 dark:text-red-300",children:["Recording actions... (",c.length," steps recorded)"]})]}),c.length>0&&o.jsxs("div",{className:"mt-2 text-xs text-red-600 dark:text-red-400",children:["Latest: ",c[c.length-1]]})]})]}),o.jsxs("div",{className:"flex-1 overflow-y-auto",children:[i.length>0&&o.jsxs("div",{className:"p-4 border-b border-gray-200 dark:border-dark-700",children:[o.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"💡 Suggested Workflows"}),o.jsx("div",{className:"space-y-3",children:i.map(y=>o.jsx("div",{className:"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3",children:o.jsxs("div",{className:"flex items-start justify-between",children:[o.jsxs("div",{className:"flex-1",children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[o.jsx("span",{className:"font-medium text-blue-900 dark:text-blue-100",children:y.name}),o.jsxs("span",{className:"text-xs px-2 py-1 bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded",children:[Math.round(y.confidence*100),"% confidence"]})]}),o.jsx("p",{className:"text-sm text-blue-700 dark:text-blue-300 mb-2",children:y.description}),o.jsxs("div",{className:"text-xs text-blue-600 dark:text-blue-400",children:["Steps: ",y.steps.join(" → ")]})]}),o.jsx("button",{onClick:()=>N(y),className:"ml-3 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700",children:"Create Workflow"})]})},y.id))})]}),o.jsxs("div",{className:"p-4",children:[o.jsx("h3",{className:"text-md font-medium text-gray-900 dark:text-white mb-3",children:"🔄 Your Workflows"}),r.length===0?o.jsxs("div",{className:"text-center py-8",children:[o.jsx("div",{className:"text-4xl mb-2",children:"🤖"}),o.jsx("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No Workflows Yet"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Start recording your actions to create automated workflows"})]}):o.jsx("div",{className:"space-y-4",children:r.map(y=>o.jsxs("div",{className:"bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-700 rounded-lg p-4",children:[o.jsxs("div",{className:"flex items-start justify-between mb-3",children:[o.jsxs("div",{className:"flex-1",children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[o.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:y.name}),y.isAutomated&&o.jsx("span",{className:"text-xs px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded",children:"Automated"}),o.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Used ",y.frequency," times"]})]}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:y.description})]}),o.jsx("button",{onClick:()=>w(y.id),disabled:j&&m===y.id,className:"btn btn-primary",children:j&&m===y.id?o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),o.jsx("span",{children:"Running..."})]}):"▶️ Execute"})]}),o.jsx("div",{className:"space-y-2",children:y.steps.map((C,E)=>o.jsxs("div",{className:"flex items-center space-x-3 p-2 bg-gray-50 dark:bg-dark-700 rounded",children:[o.jsx("span",{className:"text-lg",children:b(C.type)}),o.jsxs("span",{className:"text-sm font-mono text-gray-600 dark:text-gray-400",children:[E+1,"."]}),o.jsx("span",{className:"flex-1 text-sm text-gray-900 dark:text-white",children:C.description}),o.jsx("span",{className:`px-2 py-1 text-xs rounded ${v(C.status)}`,children:C.status})]},C.id))})]},y.id))})]})]})]})}function xm({collapsed:e,activePanel:t,onPanelChange:n}){var a;const{metrics:r}=Pn(),{sessions:s}=Mn(),{fileTree:i}=zt(),l=[{id:"terminal",name:"Terminal",icon:o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 9l3 3-3 3m5 0h3"})}),badge:s.length>0?s.length:void 0},{id:"ai",name:"AI Assistant",icon:o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})},{id:"files",name:"Explorer",icon:o.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"}),o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 1v6"})]}),badge:(a=i==null?void 0:i.children)==null?void 0:a.length},{id:"system",name:"System",icon:o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),status:r?r.cpu.usage>80||r.memory.percentage>80?"warning":"ok":void 0},{id:"analysis",name:"Code Analysis",icon:o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})},{id:"workflow",name:"Workflow",icon:o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}];return o.jsx("div",{className:`bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-700 transition-all duration-300 ${e?"w-16":"w-64"}`,children:o.jsxs("div",{className:"flex flex-col h-full",children:[o.jsx("div",{className:"p-4 border-b border-gray-200 dark:border-dark-700",children:o.jsx("div",{className:"flex flex-col space-y-2",children:l.map(u=>o.jsxs("button",{onClick:()=>n(u.id),className:`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${t===u.id?"bg-nexus-100 dark:bg-nexus-900 text-nexus-700 dark:text-nexus-300":"text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-700"}`,title:e?u.name:void 0,children:[o.jsxs("div",{className:"relative",children:[u.icon,u.status==="warning"&&o.jsx("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full"})]}),!e&&o.jsxs(o.Fragment,{children:[o.jsx("span",{className:"flex-1 text-left",children:u.name}),u.badge&&o.jsx("span",{className:"bg-gray-200 dark:bg-dark-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-full",children:u.badge})]})]},u.id))})}),o.jsx("div",{className:"flex-1 overflow-hidden",children:!e&&o.jsxs("div",{className:"h-full",children:[t==="terminal"&&o.jsx(wm,{}),t==="ai"&&o.jsx(km,{}),t==="files"&&o.jsx(jm,{}),t==="system"&&o.jsx(Nm,{}),t==="analysis"&&o.jsx(Rd,{}),t==="workflow"&&o.jsx(Od,{})]})})]})})}function wm(){const{sessions:e,createSession:t,setActiveSession:n,activeSessionId:r}=Mn();return o.jsxs("div",{className:"p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"text-sm font-semibold text-gray-900 dark:text-white",children:"Terminal Sessions"}),o.jsx("button",{onClick:()=>t(),className:"btn btn-sm btn-primary",title:"New Terminal",children:o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})})]}),o.jsxs("div",{className:"space-y-2",children:[e.map(s=>o.jsxs("div",{onClick:()=>n(s.id),className:`p-3 rounded-lg cursor-pointer transition-colors ${r===s.id?"bg-nexus-100 dark:bg-nexus-900 border border-nexus-300 dark:border-nexus-700":"bg-gray-50 dark:bg-dark-700 hover:bg-gray-100 dark:hover:bg-dark-600"}`,children:[o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:s.name}),o.jsx("div",{className:`w-2 h-2 rounded-full ${s.isActive?"bg-green-500":"bg-gray-400"}`})]}),o.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[s.blocks.length," commands"]})]},s.id)),e.length===0&&o.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[o.jsx("svg",{className:"w-8 h-8 mx-auto mb-2 opacity-50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 9l3 3-3 3m5 0h3"})}),o.jsx("p",{className:"text-sm",children:"No terminal sessions"}),o.jsx("button",{onClick:()=>t(),className:"text-nexus-600 dark:text-nexus-400 text-sm hover:underline mt-1",children:"Create one now"})]})]})]})}function km(){return o.jsxs("div",{className:"p-4",children:[o.jsx("h3",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-4",children:"AI Assistant"}),o.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[o.jsx("svg",{className:"w-8 h-8 mx-auto mb-2 opacity-50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})}),o.jsx("p",{className:"text-sm",children:"AI chat available in main panel"})]})]})}function jm(){return o.jsx(vm,{className:"h-full"})}function Nm(){const{metrics:e}=Pn();return e?o.jsxs("div",{className:"p-4",children:[o.jsx("h3",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-4",children:"System"}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"metric-card",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("span",{className:"metric-label",children:"CPU"}),o.jsxs("span",{className:"text-sm font-mono",children:[e.cpu.usage,"%"]})]}),o.jsx("div",{className:"progress-bar",children:o.jsx("div",{className:`progress-fill ${e.cpu.usage>80?"high":e.cpu.usage>60?"medium":"low"}`,style:{width:`${e.cpu.usage}%`}})})]}),o.jsxs("div",{className:"metric-card",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("span",{className:"metric-label",children:"Memory"}),o.jsxs("span",{className:"text-sm font-mono",children:[e.memory.percentage,"%"]})]}),o.jsx("div",{className:"progress-bar",children:o.jsx("div",{className:`progress-fill ${e.memory.percentage>80?"high":e.memory.percentage>60?"medium":"low"}`,style:{width:`${e.memory.percentage}%`}})})]}),o.jsxs("div",{className:"metric-card",children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsx("span",{className:"metric-label",children:"Disk"}),o.jsxs("span",{className:"text-sm font-mono",children:[e.disk.percentage,"%"]})]}),o.jsx("div",{className:"progress-bar",children:o.jsx("div",{className:`progress-fill ${e.disk.percentage>80?"high":e.disk.percentage>60?"medium":"low"}`,style:{width:`${e.disk.percentage}%`}})})]})]})]}):o.jsxs("div",{className:"p-4",children:[o.jsx("h3",{className:"text-sm font-semibold text-gray-900 dark:text-white mb-4",children:"System"}),o.jsxs("div",{className:"text-center py-8 text-gray-500 dark:text-gray-400",children:[o.jsx("div",{className:"animate-spin w-6 h-6 border-2 border-nexus-500 border-t-transparent rounded-full mx-auto mb-2"}),o.jsx("p",{className:"text-sm",children:"Loading system metrics..."})]})]})}function qa(){const{sessions:e,activeSessionId:t,executeCommand:n,createSession:r}=Mn(),{sendMessage:s}=ft(),{selectedFile:i,openFiles:l}=zt(),[a,u]=k.useState(""),[c,p]=k.useState([]),[m,g]=k.useState(-1),[j,x]=k.useState([]),[_,R]=k.useState(!1),[f,d]=k.useState(0),h=k.useRef(null),w=k.useRef(null),N=e.find(S=>S.id===t);k.useEffect(()=>{w.current&&(w.current.scrollTop=w.current.scrollHeight)},[N==null?void 0:N.blocks]),k.useEffect(()=>{h.current&&h.current.focus()},[t]),k.useEffect(()=>{(()=>{var B,X;const O=[];if(i){const T=(B=i.split(".").pop())==null?void 0:B.toLowerCase();T==="js"||T==="ts"||T==="jsx"||T==="tsx"?O.push("npm run dev","npm test","npm run build","npm install"):T==="py"?O.push("python "+i.split("/").pop(),"pip install -r requirements.txt"):T==="java"&&O.push("javac "+i.split("/").pop(),"java "+((X=i.split("/").pop())==null?void 0:X.replace(".java","")))}O.push("git status","git add .",'git commit -m ""',"git push","git pull"),O.push("ls -la","pwd","cd ..","mkdir","touch","code .","clear"),O.push("docker ps","docker build .","docker-compose up"),x(O)})()},[i,l]);const b=S=>{if(S.preventDefault(),!!a.trim()){if(!N){r();return}if(p(O=>[...O,a]),g(-1),a.startsWith("ai ")||a.startsWith("nexus ")){const O=a.replace(/^(ai|nexus)\s+/,"");s(O,{currentFile:void 0,openFiles:[],projectType:"unknown",recentCommands:c.slice(-5)})}else n(N.id,a);u("")}},v=S=>{if(_){if(S.key==="ArrowUp"){S.preventDefault(),d(O=>Math.max(0,O-1));return}else if(S.key==="ArrowDown"){S.preventDefault(),d(O=>Math.min(j.length-1,O+1));return}else if(S.key==="Tab"||S.key==="Enter"){S.preventDefault(),u(j[f]),R(!1);return}else if(S.key==="Escape"){R(!1);return}}if(S.key==="ArrowUp"){if(S.preventDefault(),m<c.length-1){const O=m+1;g(O),u(c[c.length-1-O])}}else if(S.key==="ArrowDown")if(S.preventDefault(),m>0){const O=m-1;g(O),u(c[c.length-1-O])}else m===0&&(g(-1),u(""));else if(S.key==="Tab"&&(S.preventDefault(),a.trim())){const O=j.filter(B=>B.toLowerCase().startsWith(a.toLowerCase()));O.length>0&&(x(O),R(!0),d(0))}},y=S=>{const O=S.target.value;if(u(O),O.trim()){const B=j.filter(X=>X.toLowerCase().includes(O.toLowerCase()));B.length>0?(x(B),R(!0),d(0)):R(!1)}else R(!1)},C=S=>new Date(S).toLocaleTimeString(),E=S=>S.endTime?`${S.endTime-S.startTime}ms`:"running...";return N?o.jsxs("div",{className:"flex-1 flex flex-col",children:[o.jsx("div",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-3",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsxs("div",{className:"flex space-x-2",children:[o.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),o.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),o.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"})]}),o.jsx("span",{className:"text-sm font-medium text-gray-900 dark:text-white",children:N.name}),o.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:N.workingDirectory})]}),o.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400",children:[o.jsxs("span",{children:[N.blocks.length," commands"]}),o.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]})]})}),o.jsxs("div",{ref:w,className:"flex-1 bg-black text-green-400 font-mono text-sm p-4 overflow-y-auto custom-scrollbar",children:[o.jsxs("div",{className:"mb-4 text-blue-400",children:[o.jsx("div",{children:"NEXUS AI Terminal - Intelligent Development Environment"}),o.jsx("div",{className:"text-gray-500",children:"Type 'ai <question>' or 'nexus <question>' for AI assistance"}),o.jsxs("div",{className:"text-gray-500",children:["Session: ",N.name]}),o.jsxs("div",{className:"text-gray-500",children:["Working Directory: ",N.workingDirectory]}),o.jsx("div",{children:"─".repeat(60)})]}),N.blocks.map(S=>o.jsxs("div",{className:"mb-4",children:[o.jsx("div",{className:"flex items-center justify-between mb-1 text-gray-400",children:o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsxs("span",{className:"text-blue-400",children:["Block #",S.id.slice(-4)]}),o.jsx("span",{children:C(S.startTime)}),o.jsx("span",{className:`px-2 py-1 rounded text-xs ${S.status==="completed"?"bg-green-900 text-green-300":S.status==="failed"?"bg-red-900 text-red-300":S.status==="running"?"bg-yellow-900 text-yellow-300":"bg-gray-900 text-gray-300"}`,children:S.status}),o.jsx("span",{children:E(S)})]})}),o.jsxs("div",{className:"flex items-center mb-2",children:[o.jsx("span",{className:"text-blue-400 mr-2",children:"$"}),o.jsx("span",{className:"text-white",children:S.command})]}),S.output&&o.jsx("div",{className:"ml-4 whitespace-pre-wrap text-green-400 border-l-2 border-gray-700 pl-4",children:S.output}),S.status==="running"&&o.jsxs("div",{className:"ml-4 flex items-center space-x-2 text-yellow-400",children:[o.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full"}),o.jsx("span",{children:"Executing..."})]})]},S.id)),o.jsxs("div",{className:"relative",children:[o.jsxs("form",{onSubmit:b,className:"flex items-center",children:[o.jsx("span",{className:"text-blue-400 mr-2",children:"$"}),o.jsx("input",{ref:h,type:"text",value:a,onChange:y,onKeyDown:v,className:"flex-1 bg-transparent text-white outline-none",placeholder:"Enter command or 'ai <question>' for AI assistance... (Tab for suggestions)",autoComplete:"off"})]}),_&&j.length>0&&o.jsxs("div",{className:"absolute bottom-full left-0 right-0 mb-2 bg-gray-800 border border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto z-10",children:[o.jsx("div",{className:"p-2 text-xs text-gray-400 border-b border-gray-600",children:"Suggestions (↑↓ to navigate, Tab/Enter to select, Esc to close)"}),j.map((S,O)=>o.jsx("div",{className:`px-3 py-2 cursor-pointer text-sm ${O===f?"bg-blue-600 text-white":"text-gray-300 hover:bg-gray-700"}`,onClick:()=>{u(S),R(!1)},children:o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{className:"text-blue-400",children:"$"}),o.jsx("span",{children:S})]})},O))]})]}),o.jsx("div",{className:"inline-block w-2 h-5 bg-green-400 animate-pulse ml-2"})]}),o.jsx("div",{className:"bg-gray-100 dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 px-4 py-2",children:o.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs("span",{children:["Commands: ",N.blocks.length]}),o.jsxs("span",{children:["Success: ",N.blocks.filter(S=>S.status==="completed").length]}),o.jsxs("span",{children:["Failed: ",N.blocks.filter(S=>S.status==="failed").length]})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{children:"Press ↑/↓ for history"}),o.jsx("span",{children:"•"}),o.jsx("span",{children:"Type 'ai' for AI help"})]})]})})]}):o.jsx("div",{className:"flex-1 flex items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("svg",{className:"w-16 h-16 mx-auto mb-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 9l3 3-3 3m5 0h3"})}),o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"No Terminal Session"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Create a new terminal session to get started"}),o.jsx("button",{onClick:()=>r(),className:"btn btn-primary",children:"Create Terminal Session"})]})})}function Sm({onCommand:e}){const[t,n]=k.useState(!1),[r,s]=k.useState(!1),[i,l]=k.useState(""),[a,u]=k.useState(0),[c,p]=k.useState(null),{sendMessage:m}=ft(),{executeCommand:g}=Mn(),{openFile:j,searchFiles:x}=zt(),_=k.useRef(null),R=k.useRef(null);k.useEffect(()=>{const v=window.SpeechRecognition||window.webkitSpeechRecognition;if(v){s(!0);const y=new v;y.continuous=!1,y.interimResults=!0,y.lang="en-US",y.maxAlternatives=1,y.onstart=()=>{n(!0),p(null),l("")},y.onresult=C=>{let E="",S="";for(let O=C.resultIndex;O<C.results.length;O++){const B=C.results[O];B.isFinal?(E+=B[0].transcript,u(B[0].confidence)):S+=B[0].transcript}l(E||S),E&&h(E.trim())},y.onerror=C=>{p(`Speech recognition error: ${C.error}`),n(!1)},y.onend=()=>{n(!1)},_.current=y}else s(!1),p("Speech recognition is not supported in this browser");return()=>{R.current&&clearTimeout(R.current)}},[]);const f=()=>{if(_.current&&!t)try{_.current.start(),R.current=setTimeout(()=>{d()},1e4)}catch{p("Failed to start speech recognition")}},d=()=>{_.current&&t&&(_.current.stop(),R.current&&(clearTimeout(R.current),R.current=null))},h=async v=>{console.log("Processing voice command:",v),e&&e(v);const y=v.toLowerCase();try{if(y.includes("open file")||y.includes("open the file")){const C=w(v);if(C){const E=await x(C);E.length>0?(j(E[0]),l(`Opening ${E[0]}`)):l(`File "${C}" not found`)}return}if(y.includes("run")||y.includes("execute")){const C=N(v);C&&(g(C),l(`Executing: ${C}`));return}if(y.includes("ai")||y.includes("help")||y.includes("explain")){const C=v.replace(/^(ai|help|explain)\s*/i,"");await m(C,{currentFile:null,openFiles:[],projectType:"voice-command",recentCommands:[],systemState:null}),l(`AI processing: ${C}`);return}if(y.includes("go to")||y.includes("navigate to")){const C=b(v);C&&l(`Navigating to ${C}`);return}await m(v,{currentFile:null,openFiles:[],projectType:"voice-command",recentCommands:[],systemState:null}),l(`Processing: ${v}`)}catch(C){console.error("Error processing voice command:",C),p("Failed to process voice command")}},w=v=>{const y=v.match(/open (?:the )?file (.+)/i);return y?y[1].trim():null},N=v=>{const y=v.match(/(?:run|execute) (.+)/i);return y?y[1].trim():null},b=v=>{const y=v.match(/(?:go to|navigate to) (.+)/i);return y?y[1].trim():null};return r?o.jsxs("div",{className:"p-4 bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-700 rounded-lg",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Voice Commands"}),o.jsxs("div",{className:"flex items-center space-x-2",children:[a>0&&o.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Confidence: ",Math.round(a*100),"%"]}),o.jsx("button",{onClick:t?d:f,className:`p-2 rounded-full transition-colors ${t?"bg-red-500 text-white hover:bg-red-600":"bg-blue-500 text-white hover:bg-blue-600"}`,title:t?"Stop listening":"Start voice command",children:t?o.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 11-18 0 9 9 0 0118 0z"}),o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z"})]}):o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"})})})]})]}),o.jsxs("div",{className:"mb-4",children:[t&&o.jsxs("div",{className:"flex items-center space-x-2 text-blue-600 dark:text-blue-400",children:[o.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),o.jsx("span",{className:"text-sm",children:"Listening..."})]}),i&&o.jsx("div",{className:"mt-2 p-3 bg-gray-50 dark:bg-dark-700 rounded border",children:o.jsxs("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:[o.jsx("strong",{children:"Transcript:"})," ",i]})}),c&&o.jsx("div",{className:"mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded",children:o.jsx("p",{className:"text-sm text-red-700 dark:text-red-300",children:c})})]}),o.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[o.jsx("p",{className:"font-medium mb-2",children:"Try saying:"}),o.jsxs("ul",{className:"space-y-1 text-xs",children:[o.jsx("li",{children:'• "Open file App.tsx"'}),o.jsx("li",{children:'• "Run npm install"'}),o.jsx("li",{children:'• "AI help me debug this code"'}),o.jsx("li",{children:'• "Go to terminal"'}),o.jsx("li",{children:'• "Explain this function"'})]})]})]}):o.jsx("div",{className:"p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg",children:o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("svg",{className:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})}),o.jsx("span",{className:"text-sm text-yellow-800 dark:text-yellow-200",children:"Voice commands are not supported in this browser"})]})})}function Cm(){var b;const{providers:e,selectedProvider:t,setSelectedProvider:n,chatHistory:r,isLoading:s,sendMessage:i,clearHistory:l}=ft(),{metrics:a}=Pn(),{selectedFile:u,openFiles:c}=zt(),[p,m]=k.useState(""),[g,j]=k.useState(!1),[x,_]=k.useState([]),R=k.useRef(null),f=k.useRef(null);k.useEffect(()=>{var v;(v=R.current)==null||v.scrollIntoView({behavior:"smooth"})},[r]),k.useEffect(()=>{var v;(v=f.current)==null||v.focus()},[]),k.useEffect(()=>{(()=>{const y=[];u&&(y.push(`Analyze ${u.split("/").pop()}`),y.push(`Explain this code in ${u}`),y.push(`Find bugs in ${u}`),y.push(`Optimize ${u}`)),c.length>1&&(y.push("Compare these open files"),y.push("Refactor common code across files")),(a==null?void 0:a.memory.percentage)>80&&y.push("Help optimize memory usage"),(a==null?void 0:a.cpu.usage)>80&&y.push("Analyze high CPU usage"),y.push("Generate unit tests"),y.push("Review code quality"),y.push("Suggest improvements"),_(y.slice(0,4))})()},[u,c,a]);const d=async v=>{if(v.preventDefault(),!p.trim()||s)return;const y=p.trim();m("");const C={currentFile:u,openFiles:c,projectType:"nexus-ai",recentCommands:[],systemState:a||{cpu:{usage:0,temperature:0,cores:0},memory:{total:0,used:0,free:0,percentage:0},disk:{total:0,used:0,free:0,percentage:0},network:{upload:0,download:0,latency:0},timestamp:Date.now()}};await i(y,C)},h=v=>{v.key==="Enter"&&!v.shiftKey&&(v.preventDefault(),d(v))},w=v=>new Date(v).toLocaleTimeString(),N=v=>{var E,S;const y=v.type==="user",C=v.type==="system";return o.jsx("div",{className:`mb-6 ${y?"ml-8":"mr-8"}`,children:o.jsxs("div",{className:`ai-chat-message ${v.type}`,children:[o.jsxs("div",{className:"flex items-center justify-between mb-2",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${y?"bg-nexus-600 text-white":C?"bg-yellow-600 text-white":"bg-gray-600 text-white"}`,children:y?"U":C?"S":"AI"}),o.jsx("span",{className:"text-sm font-medium",children:y?"You":C?"System":"NEXUS AI"}),((E=v.metadata)==null?void 0:E.provider)&&o.jsxs("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["via ",(S=e.find(O=>O.id===v.metadata.provider))==null?void 0:S.name]})]}),o.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:w(v.timestamp)})]}),o.jsx("div",{className:"prose prose-sm dark:prose-invert max-w-none",children:o.jsx("div",{className:"whitespace-pre-wrap",children:v.content})}),v.metadata&&!y&&o.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200 dark:border-gray-600",children:[o.jsx("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:o.jsxs("div",{className:"flex items-center space-x-4",children:[v.metadata.confidence&&o.jsxs("span",{children:["Confidence: ",Math.round(v.metadata.confidence*100),"%"]}),v.metadata.provider&&o.jsxs("span",{children:["Provider: ",v.metadata.provider]})]})}),v.metadata.suggestions&&v.metadata.suggestions.length>0&&o.jsxs("div",{className:"mt-2",children:[o.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 mb-1",children:"Suggestions:"}),o.jsx("div",{className:"flex flex-wrap gap-1",children:v.metadata.suggestions.map((O,B)=>o.jsx("button",{onClick:()=>m(O),className:"px-2 py-1 bg-gray-200 dark:bg-gray-700 text-xs rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors",children:O},B))})]})]})]})},v.id)};return o.jsxs("div",{className:"flex-1 flex flex-col",children:[o.jsx("div",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-3",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-nexus-500 to-nexus-700 rounded-lg flex items-center justify-center",children:o.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),o.jsxs("div",{children:[o.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"NEXUS AI Assistant"}),o.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Your intelligent development companion"})]})]}),o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsxs("select",{value:t||"",onChange:v=>n(v.target.value||null),className:"input text-sm",children:[o.jsx("option",{value:"",children:"Auto-select provider"}),e.filter(v=>v.isAvailable).map(v=>o.jsx("option",{value:v.id,children:v.name},v.id))]}),o.jsx("button",{onClick:l,className:"btn btn-sm btn-secondary",title:"Clear chat history",children:o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})}),o.jsx("div",{className:"flex-1 overflow-y-auto p-4 custom-scrollbar",children:r.length===0?o.jsx("div",{className:"flex-1 flex items-center justify-center",children:o.jsxs("div",{className:"text-center max-w-md",children:[o.jsx("div",{className:"w-16 h-16 bg-gradient-to-br from-nexus-500 to-nexus-700 rounded-full flex items-center justify-center mx-auto mb-4",children:o.jsx("svg",{className:"w-8 h-8 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Welcome to NEXUS AI"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"I'm your intelligent development assistant. I can help you with coding, debugging, system optimization, and much more. Ask me anything!"}),o.jsxs("div",{className:"grid grid-cols-1 gap-2 text-sm",children:[o.jsx("button",{onClick:()=>m("What can you help me with?"),className:"p-3 bg-gray-100 dark:bg-dark-700 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors text-left",children:"💡 What can you help me with?"}),o.jsx("button",{onClick:()=>m("Analyze my system performance"),className:"p-3 bg-gray-100 dark:bg-dark-700 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors text-left",children:"📊 Analyze my system performance"}),o.jsx("button",{onClick:()=>m("Help me optimize my development workflow"),className:"p-3 bg-gray-100 dark:bg-dark-700 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors text-left",children:"⚡ Help me optimize my development workflow"})]})]})}):o.jsxs(o.Fragment,{children:[r.map(N),s&&o.jsx("div",{className:"mr-8 mb-6",children:o.jsxs("div",{className:"ai-chat-message assistant",children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx("div",{className:"w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-xs font-bold text-white",children:"AI"}),o.jsx("span",{className:"text-sm font-medium",children:"NEXUS AI"}),o.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"thinking..."})]}),o.jsxs("div",{className:"typing-indicator",children:[o.jsx("div",{className:"typing-dot"}),o.jsx("div",{className:"typing-dot"}),o.jsx("div",{className:"typing-dot"})]})]})}),o.jsx("div",{ref:R})]})}),g&&o.jsx("div",{className:"border-t border-gray-200 dark:border-dark-700",children:o.jsx(Sm,{onCommand:v=>{m(v),j(!1)}})}),x.length>0&&o.jsxs("div",{className:"bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800 p-3",children:[o.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[o.jsx("svg",{className:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})}),o.jsx("span",{className:"text-sm font-medium text-blue-700 dark:text-blue-300",children:"AI Suggestions"})]}),o.jsx("div",{className:"flex flex-wrap gap-2",children:x.map((v,y)=>o.jsx("button",{onClick:()=>{m(v),_([])},className:"px-3 py-1 text-sm bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded-full hover:bg-blue-200 dark:hover:bg-blue-700 transition-colors",children:v},y))})]}),o.jsxs("div",{className:"bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 p-4",children:[o.jsxs("form",{onSubmit:d,className:"flex space-x-3",children:[o.jsxs("div",{className:"flex-1 relative",children:[o.jsx("textarea",{ref:f,value:p,onChange:v=>m(v.target.value),onKeyDown:h,placeholder:"Ask NEXUS AI anything... (Shift+Enter for new line)",className:"input resize-none pr-12",rows:1,disabled:s}),o.jsx("button",{type:"button",onClick:()=>j(!g),className:"absolute right-2 top-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"Voice Command",children:o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"})})})]}),o.jsx("button",{type:"submit",disabled:!p.trim()||s,className:"btn btn-primary px-6",children:s?o.jsx("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}):o.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})})]}),o.jsxs("div",{className:"flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs("span",{children:["Provider: ",t?((b=e.find(v=>v.id===t))==null?void 0:b.name)||"Unknown":"Auto-select"]}),o.jsxs("span",{children:["Messages: ",r.length]})]}),o.jsx("span",{children:"Press Shift+Enter for new line"})]})]})]})}function Em(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ka(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),n.push.apply(n,r)}return n}function Ya(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Ka(Object(n),!0).forEach(function(r){Em(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ka(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function _m(e,t){if(e==null)return{};var n={},r=Object.keys(e),s,i;for(i=0;i<r.length;i++)s=r[i],!(t.indexOf(s)>=0)&&(n[s]=e[s]);return n}function bm(e,t){if(e==null)return{};var n=_m(e,t),r,s;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)r=i[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Lm(e,t){return Tm(e)||Pm(e,t)||Mm(e,t)||Rm()}function Tm(e){if(Array.isArray(e))return e}function Pm(e,t){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var n=[],r=!0,s=!1,i=void 0;try{for(var l=e[Symbol.iterator](),a;!(r=(a=l.next()).done)&&(n.push(a.value),!(t&&n.length===t));r=!0);}catch(u){s=!0,i=u}finally{try{!r&&l.return!=null&&l.return()}finally{if(s)throw i}}return n}}function Mm(e,t){if(e){if(typeof e=="string")return Xa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Xa(e,t)}}function Xa(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Rm(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Om(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ga(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(s){return Object.getOwnPropertyDescriptor(e,s).enumerable})),n.push.apply(n,r)}return n}function Ja(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Ga(Object(n),!0).forEach(function(r){Om(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ga(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Am(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){return t.reduceRight(function(s,i){return i(s)},r)}}function qn(e){return function t(){for(var n=this,r=arguments.length,s=new Array(r),i=0;i<r;i++)s[i]=arguments[i];return s.length>=e.length?e.apply(this,s):function(){for(var l=arguments.length,a=new Array(l),u=0;u<l;u++)a[u]=arguments[u];return t.apply(n,[].concat(s,a))}}}function zs(e){return{}.toString.call(e).includes("Object")}function zm(e){return!Object.keys(e).length}function kr(e){return typeof e=="function"}function Im(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function Fm(e,t){return zs(t)||Tt("changeType"),Object.keys(t).some(function(n){return!Im(e,n)})&&Tt("changeField"),t}function Dm(e){kr(e)||Tt("selectorType")}function Bm(e){kr(e)||zs(e)||Tt("handlerType"),zs(e)&&Object.values(e).some(function(t){return!kr(t)})&&Tt("handlersType")}function $m(e){e||Tt("initialIsRequired"),zs(e)||Tt("initialType"),zm(e)&&Tt("initialContent")}function Um(e,t){throw new Error(e[t]||e.default)}var Vm={initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"},Tt=qn(Um)(Vm),Kr={changes:Fm,selector:Dm,handler:Bm,initial:$m};function Wm(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};Kr.initial(e),Kr.handler(t);var n={current:e},r=qn(qm)(n,t),s=qn(Qm)(n),i=qn(Kr.changes)(e),l=qn(Hm)(n);function a(){var c=arguments.length>0&&arguments[0]!==void 0?arguments[0]:function(p){return p};return Kr.selector(c),c(n.current)}function u(c){Am(r,s,i,l)(c)}return[a,u]}function Hm(e,t){return kr(t)?t(e.current):t}function Qm(e,t){return e.current=Ja(Ja({},e.current),t),t}function qm(e,t,n){return kr(t)?t(e.current):Object.keys(n).forEach(function(r){var s;return(s=t[r])===null||s===void 0?void 0:s.call(t,e.current[r])}),n}var Km={create:Wm},Ym={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.52.2/min/vs"}};function Xm(e){return function t(){for(var n=this,r=arguments.length,s=new Array(r),i=0;i<r;i++)s[i]=arguments[i];return s.length>=e.length?e.apply(this,s):function(){for(var l=arguments.length,a=new Array(l),u=0;u<l;u++)a[u]=arguments[u];return t.apply(n,[].concat(s,a))}}}function Gm(e){return{}.toString.call(e).includes("Object")}function Jm(e){return e||Za("configIsRequired"),Gm(e)||Za("configType"),e.urls?(Zm(),{paths:{vs:e.urls.monacoBase}}):e}function Zm(){console.warn(Ad.deprecation)}function e0(e,t){throw new Error(e[t]||e.default)}var Ad={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:`Deprecation warning!
    You are using deprecated way of configuration.

    Instead of using
      monaco.config({ urls: { monacoBase: '...' } })
    use
      monaco.config({ paths: { vs: '...' } })

    For more please check the link https://github.com/suren-atoyan/monaco-loader#config
  `},Za=Xm(e0)(Ad),t0={config:Jm},n0=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(s){return n.reduceRight(function(i,l){return l(i)},s)}};function zd(e,t){return Object.keys(t).forEach(function(n){t[n]instanceof Object&&e[n]&&Object.assign(t[n],zd(e[n],t[n]))}),Ya(Ya({},e),t)}var r0={type:"cancelation",msg:"operation is manually canceled"};function Pi(e){var t=!1,n=new Promise(function(r,s){e.then(function(i){return t?s(r0):r(i)}),e.catch(s)});return n.cancel=function(){return t=!0},n}var s0=Km.create({config:Ym,isInitialized:!1,resolve:null,reject:null,monaco:null}),Id=Lm(s0,2),_r=Id[0],ti=Id[1];function i0(e){var t=t0.config(e),n=t.monaco,r=bm(t,["monaco"]);ti(function(s){return{config:zd(s.config,r),monaco:n}})}function o0(){var e=_r(function(t){var n=t.monaco,r=t.isInitialized,s=t.resolve;return{monaco:n,isInitialized:r,resolve:s}});if(!e.isInitialized){if(ti({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),Pi(Mi);if(window.monaco&&window.monaco.editor)return Fd(window.monaco),e.resolve(window.monaco),Pi(Mi);n0(l0,u0)(c0)}return Pi(Mi)}function l0(e){return document.body.appendChild(e)}function a0(e){var t=document.createElement("script");return e&&(t.src=e),t}function u0(e){var t=_r(function(r){var s=r.config,i=r.reject;return{config:s,reject:i}}),n=a0("".concat(t.config.paths.vs,"/loader.js"));return n.onload=function(){return e()},n.onerror=t.reject,n}function c0(){var e=_r(function(n){var r=n.config,s=n.resolve,i=n.reject;return{config:r,resolve:s,reject:i}}),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],function(n){Fd(n),e.resolve(n)},function(n){e.reject(n)})}function Fd(e){_r().monaco||ti({monaco:e})}function d0(){return _r(function(e){var t=e.monaco;return t})}var Mi=new Promise(function(e,t){return ti({resolve:e,reject:t})}),Dd={config:i0,init:o0,__getMonacoInstance:d0},f0={wrapper:{display:"flex",position:"relative",textAlign:"initial"},fullWidth:{width:"100%"},hide:{display:"none"}},Ri=f0,h0={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},p0=h0;function m0({children:e}){return wt.createElement("div",{style:p0.container},e)}var g0=m0,y0=g0;function v0({width:e,height:t,isEditorReady:n,loading:r,_ref:s,className:i,wrapperProps:l}){return wt.createElement("section",{style:{...Ri.wrapper,width:e,height:t},...l},!n&&wt.createElement(y0,null,r),wt.createElement("div",{ref:s,style:{...Ri.fullWidth,...!n&&Ri.hide},className:i}))}var x0=v0,Bd=k.memo(x0);function w0(e){k.useEffect(e,[])}var $d=w0;function k0(e,t,n=!0){let r=k.useRef(!0);k.useEffect(r.current||!n?()=>{r.current=!1}:e,t)}var _e=k0;function rr(){}function fn(e,t,n,r){return j0(e,r)||N0(e,t,n,r)}function j0(e,t){return e.editor.getModel(Ud(e,t))}function N0(e,t,n,r){return e.editor.createModel(t,n,r?Ud(e,r):void 0)}function Ud(e,t){return e.Uri.parse(t)}function S0({original:e,modified:t,language:n,originalLanguage:r,modifiedLanguage:s,originalModelPath:i,modifiedModelPath:l,keepCurrentOriginalModel:a=!1,keepCurrentModifiedModel:u=!1,theme:c="light",loading:p="Loading...",options:m={},height:g="100%",width:j="100%",className:x,wrapperProps:_={},beforeMount:R=rr,onMount:f=rr}){let[d,h]=k.useState(!1),[w,N]=k.useState(!0),b=k.useRef(null),v=k.useRef(null),y=k.useRef(null),C=k.useRef(f),E=k.useRef(R),S=k.useRef(!1);$d(()=>{let T=Dd.init();return T.then(A=>(v.current=A)&&N(!1)).catch(A=>(A==null?void 0:A.type)!=="cancelation"&&console.error("Monaco initialization: error:",A)),()=>b.current?X():T.cancel()}),_e(()=>{if(b.current&&v.current){let T=b.current.getOriginalEditor(),A=fn(v.current,e||"",r||n||"text",i||"");A!==T.getModel()&&T.setModel(A)}},[i],d),_e(()=>{if(b.current&&v.current){let T=b.current.getModifiedEditor(),A=fn(v.current,t||"",s||n||"text",l||"");A!==T.getModel()&&T.setModel(A)}},[l],d),_e(()=>{let T=b.current.getModifiedEditor();T.getOption(v.current.editor.EditorOption.readOnly)?T.setValue(t||""):t!==T.getValue()&&(T.executeEdits("",[{range:T.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),T.pushUndoStop())},[t],d),_e(()=>{var T,A;(A=(T=b.current)==null?void 0:T.getModel())==null||A.original.setValue(e||"")},[e],d),_e(()=>{let{original:T,modified:A}=b.current.getModel();v.current.editor.setModelLanguage(T,r||n||"text"),v.current.editor.setModelLanguage(A,s||n||"text")},[n,r,s],d),_e(()=>{var T;(T=v.current)==null||T.editor.setTheme(c)},[c],d),_e(()=>{var T;(T=b.current)==null||T.updateOptions(m)},[m],d);let O=k.useCallback(()=>{var $;if(!v.current)return;E.current(v.current);let T=fn(v.current,e||"",r||n||"text",i||""),A=fn(v.current,t||"",s||n||"text",l||"");($=b.current)==null||$.setModel({original:T,modified:A})},[n,t,s,e,r,i,l]),B=k.useCallback(()=>{var T;!S.current&&y.current&&(b.current=v.current.editor.createDiffEditor(y.current,{automaticLayout:!0,...m}),O(),(T=v.current)==null||T.editor.setTheme(c),h(!0),S.current=!0)},[m,c,O]);k.useEffect(()=>{d&&C.current(b.current,v.current)},[d]),k.useEffect(()=>{!w&&!d&&B()},[w,d,B]);function X(){var A,$,P,I;let T=(A=b.current)==null?void 0:A.getModel();a||(($=T==null?void 0:T.original)==null||$.dispose()),u||((P=T==null?void 0:T.modified)==null||P.dispose()),(I=b.current)==null||I.dispose()}return wt.createElement(Bd,{width:j,height:g,isEditorReady:d,loading:p,_ref:y,className:x,wrapperProps:_})}var C0=S0;k.memo(C0);function E0(e){let t=k.useRef();return k.useEffect(()=>{t.current=e},[e]),t.current}var _0=E0,Yr=new Map;function b0({defaultValue:e,defaultLanguage:t,defaultPath:n,value:r,language:s,path:i,theme:l="light",line:a,loading:u="Loading...",options:c={},overrideServices:p={},saveViewState:m=!0,keepCurrentModel:g=!1,width:j="100%",height:x="100%",className:_,wrapperProps:R={},beforeMount:f=rr,onMount:d=rr,onChange:h,onValidate:w=rr}){let[N,b]=k.useState(!1),[v,y]=k.useState(!0),C=k.useRef(null),E=k.useRef(null),S=k.useRef(null),O=k.useRef(d),B=k.useRef(f),X=k.useRef(),T=k.useRef(r),A=_0(i),$=k.useRef(!1),P=k.useRef(!1);$d(()=>{let M=Dd.init();return M.then(F=>(C.current=F)&&y(!1)).catch(F=>(F==null?void 0:F.type)!=="cancelation"&&console.error("Monaco initialization: error:",F)),()=>E.current?D():M.cancel()}),_e(()=>{var F,ee,Ce,Ke;let M=fn(C.current,e||r||"",t||s||"",i||n||"");M!==((F=E.current)==null?void 0:F.getModel())&&(m&&Yr.set(A,(ee=E.current)==null?void 0:ee.saveViewState()),(Ce=E.current)==null||Ce.setModel(M),m&&((Ke=E.current)==null||Ke.restoreViewState(Yr.get(i))))},[i],N),_e(()=>{var M;(M=E.current)==null||M.updateOptions(c)},[c],N),_e(()=>{!E.current||r===void 0||(E.current.getOption(C.current.editor.EditorOption.readOnly)?E.current.setValue(r):r!==E.current.getValue()&&(P.current=!0,E.current.executeEdits("",[{range:E.current.getModel().getFullModelRange(),text:r,forceMoveMarkers:!0}]),E.current.pushUndoStop(),P.current=!1))},[r],N),_e(()=>{var F,ee;let M=(F=E.current)==null?void 0:F.getModel();M&&s&&((ee=C.current)==null||ee.editor.setModelLanguage(M,s))},[s],N),_e(()=>{var M;a!==void 0&&((M=E.current)==null||M.revealLine(a))},[a],N),_e(()=>{var M;(M=C.current)==null||M.editor.setTheme(l)},[l],N);let I=k.useCallback(()=>{var M;if(!(!S.current||!C.current)&&!$.current){B.current(C.current);let F=i||n,ee=fn(C.current,r||e||"",t||s||"",F||"");E.current=(M=C.current)==null?void 0:M.editor.create(S.current,{model:ee,automaticLayout:!0,...c},p),m&&E.current.restoreViewState(Yr.get(F)),C.current.editor.setTheme(l),a!==void 0&&E.current.revealLine(a),b(!0),$.current=!0}},[e,t,n,r,s,i,c,p,m,l,a]);k.useEffect(()=>{N&&O.current(E.current,C.current)},[N]),k.useEffect(()=>{!v&&!N&&I()},[v,N,I]),T.current=r,k.useEffect(()=>{var M,F;N&&h&&((M=X.current)==null||M.dispose(),X.current=(F=E.current)==null?void 0:F.onDidChangeModelContent(ee=>{P.current||h(E.current.getValue(),ee)}))},[N,h]),k.useEffect(()=>{if(N){let M=C.current.editor.onDidChangeMarkers(F=>{var Ce;let ee=(Ce=E.current.getModel())==null?void 0:Ce.uri;if(ee&&F.find(Ke=>Ke.path===ee.path)){let Ke=C.current.editor.getModelMarkers({resource:ee});w==null||w(Ke)}});return()=>{M==null||M.dispose()}}return()=>{}},[N,w]);function D(){var M,F;(M=X.current)==null||M.dispose(),g?m&&Yr.set(i,E.current.saveViewState()):(F=E.current.getModel())==null||F.dispose(),E.current.dispose()}return wt.createElement(Bd,{width:j,height:x,isEditorReady:N,loading:u,_ref:S,className:_,wrapperProps:R})}var L0=b0,T0=k.memo(L0),P0=T0;function M0(){const{selectedFile:e,openFiles:t,closeFile:n,fileContents:r,updateFileContent:s}=zt(),{theme:i}=_d(),{sendMessage:l}=ft(),[a,u]=k.useState(""),[c,p]=k.useState(!1),[m,g]=k.useState("typescript");k.useEffect(()=>{var f;if(e&&r[e]){u(r[e]),p(!1);const d=(f=e.split(".").pop())==null?void 0:f.toLowerCase();g({ts:"typescript",tsx:"typescript",js:"javascript",jsx:"javascript",py:"python",java:"java",cpp:"cpp",c:"c",cs:"csharp",php:"php",rb:"ruby",go:"go",rs:"rust",html:"html",css:"css",scss:"scss",json:"json",xml:"xml",yaml:"yaml",yml:"yaml",md:"markdown",sql:"sql",sh:"shell",bash:"shell"}[d||""]||"plaintext")}},[e,r]);const j=f=>{f!==void 0&&(u(f),p(f!==(r[e||""]||"")))},x=()=>{e&&c&&(s(e,a),p(!1))},_=f=>{(f.ctrlKey||f.metaKey)&&f.key==="s"&&(f.preventDefault(),x())};k.useEffect(()=>(document.addEventListener("keydown",_),()=>document.removeEventListener("keydown",_)),[e,c,a]);const R=async()=>{!e||!a||await l(`Analyze this ${m} code and provide suggestions for improvement:

${a}`,{currentFile:e,openFiles:t,projectType:"nexus-ai",recentCommands:[],systemState:null})};return o.jsxs("div",{className:"flex-1 flex flex-col",children:[t.length>0&&o.jsx("div",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700",children:o.jsx("div",{className:"flex overflow-x-auto",children:t.map(f=>o.jsxs("div",{className:`flex items-center space-x-2 px-4 py-2 border-r border-gray-200 dark:border-dark-700 ${e===f?"bg-gray-50 dark:bg-dark-700":"hover:bg-gray-50 dark:hover:bg-dark-700"}`,children:[o.jsxs("span",{className:"text-sm flex items-center space-x-1",children:[o.jsx("span",{children:f.split("/").pop()}),c&&e===f&&o.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),o.jsx("button",{onClick:()=>n(f),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]},f))})}),e&&o.jsx("div",{className:"bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-2",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e}),o.jsx("span",{className:"text-xs px-2 py-1 bg-gray-100 dark:bg-dark-700 rounded",children:m})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("button",{onClick:R,className:"px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"🤖 AI Analyze"}),o.jsxs("button",{onClick:x,disabled:!c,className:`px-3 py-1 text-xs rounded transition-colors ${c?"bg-green-500 text-white hover:bg-green-600":"bg-gray-200 dark:bg-dark-700 text-gray-500 cursor-not-allowed"}`,children:["Save ",c&&"•"]})]})]})}),o.jsx("div",{className:"flex-1",children:e?o.jsx(P0,{height:"100%",language:m,value:a,onChange:j,theme:i==="dark"?"vs-dark":"light",options:{minimap:{enabled:!0},fontSize:14,lineNumbers:"on",roundedSelection:!1,scrollBeyondLastLine:!1,automaticLayout:!0,tabSize:2,insertSpaces:!0,wordWrap:"on",bracketPairColorization:{enabled:!0},guides:{bracketPairs:!0,indentation:!0},suggest:{showKeywords:!0,showSnippets:!0,showFunctions:!0,showConstructors:!0,showFields:!0,showVariables:!0,showClasses:!0,showStructs:!0,showInterfaces:!0,showModules:!0,showProperties:!0,showEvents:!0,showOperators:!0,showUnits:!0,showValues:!0,showConstants:!0,showEnums:!0,showEnumMembers:!0,showColors:!0,showFiles:!0,showReferences:!0,showFolders:!0,showTypeParameters:!0,showIssues:!0,showUsers:!0},quickSuggestions:{other:!0,comments:!0,strings:!0},parameterHints:{enabled:!0},hover:{enabled:!0},contextmenu:!0,mouseWheelZoom:!0,cursorBlinking:"blink",cursorSmoothCaretAnimation:"on",smoothScrolling:!0}}):o.jsx("div",{className:"flex-1 flex items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("svg",{className:"w-16 h-16 mx-auto mb-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Advanced Code Editor"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:"Select a file from the sidebar to start editing"}),o.jsxs("div",{className:"text-sm text-gray-500 dark:text-gray-400 space-y-1",children:[o.jsx("p",{children:"✨ Features:"}),o.jsx("p",{children:"• Syntax highlighting for 30+ languages"}),o.jsx("p",{children:"• AI-powered code analysis"}),o.jsx("p",{children:"• Intelligent autocomplete"}),o.jsx("p",{children:"• Real-time error detection"}),o.jsx("p",{children:"• Ctrl+S to save"})]})]})})})]})}function R0(){var p,m,g,j;const{metrics:e,processes:t,systemInfo:n,isMonitoring:r}=Pn(),{sendMessage:s}=ft(),[i,l]=k.useState([]),[a,u]=k.useState(!1);k.useEffect(()=>{if(!e)return;const x=[];e.memory.percentage>85&&x.push({id:"memory-high",type:"warning",title:"High Memory Usage",message:`Memory usage is at ${e.memory.percentage}%. Consider closing unused applications.`,timestamp:new Date,action:"optimize_memory"}),e.cpu.usage>90&&x.push({id:"cpu-high",type:"error",title:"High CPU Usage",message:`CPU usage is at ${e.cpu.usage}%. System may become unresponsive.`,timestamp:new Date,action:"analyze_cpu"}),e.disk.percentage>90&&x.push({id:"disk-low",type:"warning",title:"Low Disk Space",message:`Disk usage is at ${e.disk.percentage}%. Consider cleaning up files.`,timestamp:new Date,action:"cleanup_disk"}),e.cpu.temperature>80&&x.push({id:"temp-high",type:"error",title:"High CPU Temperature",message:`CPU temperature is ${e.cpu.temperature}°C. Check cooling system.`,timestamp:new Date,action:"check_cooling"}),l(x)},[e]);const c=async x=>{const _={currentFile:null,openFiles:[],projectType:"system-analysis",recentCommands:[],systemState:e};let R="";switch(x){case"performance":R="Analyze current system performance and suggest optimizations";break;case"memory":R="Help me optimize memory usage and identify memory leaks";break;case"cpu":R="Analyze high CPU usage and suggest solutions";break;case"disk":R="Help me clean up disk space and optimize storage";break;default:R="Provide general system health analysis and recommendations"}await s(R,_),u(!0)};return!r||!e?o.jsx("div",{className:"flex-1 flex items-center justify-center",children:o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"animate-spin w-8 h-8 border-4 border-nexus-500 border-t-transparent rounded-full mx-auto mb-4"}),o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"Loading System Data"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Initializing system monitoring..."})]})}):o.jsx("div",{className:"flex-1 p-6 overflow-y-auto custom-scrollbar",children:o.jsxs("div",{className:"max-w-7xl mx-auto",children:[o.jsx("div",{className:"mb-8",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"System Dashboard"}),o.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Real-time monitoring of your development environment"})]}),o.jsxs("div",{className:"flex space-x-2",children:[o.jsx("button",{onClick:()=>c("performance"),className:"btn btn-primary",children:"🤖 AI Analysis"}),o.jsx("button",{onClick:()=>u(!a),className:"btn btn-secondary",children:"📊 Insights"})]})]})}),i.length>0&&o.jsxs("div",{className:"mb-8",children:[o.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"System Alerts"}),o.jsx("div",{className:"space-y-3",children:i.map(x=>o.jsx("div",{className:`p-4 rounded-lg border-l-4 ${x.type==="error"?"bg-red-50 dark:bg-red-900/20 border-red-500":x.type==="warning"?"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-500":"bg-blue-50 dark:bg-blue-900/20 border-blue-500"}`,children:o.jsxs("div",{className:"flex items-start justify-between",children:[o.jsxs("div",{className:"flex-1",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("span",{className:`text-sm font-medium ${x.type==="error"?"text-red-800 dark:text-red-200":x.type==="warning"?"text-yellow-800 dark:text-yellow-200":"text-blue-800 dark:text-blue-200"}`,children:x.title}),o.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:x.timestamp.toLocaleTimeString()})]}),o.jsx("p",{className:`text-sm mt-1 ${x.type==="error"?"text-red-700 dark:text-red-300":x.type==="warning"?"text-yellow-700 dark:text-yellow-300":"text-blue-700 dark:text-blue-300"}`,children:x.message})]}),x.action&&o.jsx("button",{onClick:()=>c(x.action),className:`ml-4 px-3 py-1 text-xs rounded ${x.type==="error"?"bg-red-600 text-white hover:bg-red-700":x.type==="warning"?"bg-yellow-600 text-white hover:bg-yellow-700":"bg-blue-600 text-white hover:bg-blue-700"}`,children:"Fix with AI"})]})},x.id))})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[o.jsxs("div",{className:"metric-card",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center",children:o.jsx("svg",{className:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"})})}),o.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:"CPU"})]}),o.jsxs("span",{className:"metric-value text-blue-600 dark:text-blue-400",children:[e.cpu.usage,"%"]})]}),o.jsx("div",{className:"progress-bar",children:o.jsx("div",{className:`progress-fill ${e.cpu.usage>80?"high":e.cpu.usage>60?"medium":"low"}`,style:{width:`${e.cpu.usage}%`}})}),o.jsxs("div",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:[e.cpu.cores," cores • ",e.cpu.temperature>0?`${e.cpu.temperature}°C`:"Temp N/A"]})]}),o.jsxs("div",{className:"metric-card",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center",children:o.jsx("svg",{className:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})})}),o.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:"Memory"})]}),o.jsxs("span",{className:"metric-value text-green-600 dark:text-green-400",children:[e.memory.percentage,"%"]})]}),o.jsx("div",{className:"progress-bar",children:o.jsx("div",{className:`progress-fill ${e.memory.percentage>80?"high":e.memory.percentage>60?"medium":"low"}`,style:{width:`${e.memory.percentage}%`}})}),o.jsxs("div",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:[(e.memory.used/1024/1024/1024).toFixed(1),"GB / ",(e.memory.total/1024/1024/1024).toFixed(1),"GB"]})]}),o.jsxs("div",{className:"metric-card",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center",children:o.jsx("svg",{className:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"})})}),o.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:"Disk"})]}),o.jsxs("span",{className:"metric-value text-purple-600 dark:text-purple-400",children:[e.disk.percentage,"%"]})]}),o.jsx("div",{className:"progress-bar",children:o.jsx("div",{className:`progress-fill ${e.disk.percentage>80?"high":e.disk.percentage>60?"medium":"low"}`,style:{width:`${e.disk.percentage}%`}})}),o.jsxs("div",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:[(e.disk.used/1024/1024/1024).toFixed(1),"GB / ",(e.disk.total/1024/1024/1024).toFixed(1),"GB"]})]}),o.jsxs("div",{className:"metric-card",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center",children:o.jsx("svg",{className:"w-5 h-5 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"})})}),o.jsx("span",{className:"font-medium text-gray-900 dark:text-white",children:"Network"})]}),o.jsxs("span",{className:"metric-value text-orange-600 dark:text-orange-400",children:[e.network.latency,"ms"]})]}),o.jsxs("div",{className:"flex justify-between text-sm text-gray-600 dark:text-gray-400",children:[o.jsxs("span",{children:["↑ ",(e.network.upload/1024).toFixed(1)," KB/s"]}),o.jsxs("span",{children:["↓ ",(e.network.download/1024).toFixed(1)," KB/s"]})]})]})]}),n&&o.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[o.jsxs("div",{className:"bg-white dark:bg-dark-800 p-6 rounded-lg border border-gray-200 dark:border-dark-700",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"System Information"}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Platform:"}),o.jsx("span",{className:"font-mono text-sm",children:(p=n.os)==null?void 0:p.platform})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"OS:"}),o.jsx("span",{className:"font-mono text-sm",children:(m=n.os)==null?void 0:m.distro})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"Architecture:"}),o.jsx("span",{className:"font-mono text-sm",children:(g=n.os)==null?void 0:g.arch})]}),o.jsxs("div",{className:"flex justify-between",children:[o.jsx("span",{className:"text-gray-600 dark:text-gray-400",children:"CPU:"}),o.jsx("span",{className:"font-mono text-sm",children:(j=n.cpu)==null?void 0:j.brand})]})]})]}),o.jsxs("div",{className:"bg-white dark:bg-dark-800 p-6 rounded-lg border border-gray-200 dark:border-dark-700",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Top Processes"}),o.jsx("div",{className:"space-y-2",children:t.slice(0,5).map((x,_)=>o.jsxs("div",{className:"flex items-center justify-between text-sm",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsxs("span",{className:"w-6 text-center text-gray-500",children:["#",_+1]}),o.jsx("span",{className:"font-mono",children:x.name})]}),o.jsxs("div",{className:"flex items-center space-x-3 text-gray-600 dark:text-gray-400",children:[o.jsxs("span",{children:[x.cpu.toFixed(1),"%"]}),o.jsxs("span",{children:[x.memory.toFixed(1),"%"]})]})]},x.pid))})]})]}),o.jsxs("div",{className:"bg-white dark:bg-dark-800 p-6 rounded-lg border border-gray-200 dark:border-dark-700",children:[o.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Performance History"}),o.jsx("div",{className:"h-64 flex items-center justify-center text-gray-500 dark:text-gray-400",children:o.jsxs("div",{className:"text-center",children:[o.jsx("svg",{className:"w-12 h-12 mx-auto mb-2 opacity-50",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),o.jsx("p",{children:"Real-time charts will be implemented in Stage 2"})]})})]})]})})}function O0({activePanel:e,sidebarCollapsed:t}){const n=()=>{switch(e){case"terminal":return o.jsx(qa,{});case"ai":return o.jsx(Cm,{});case"files":return o.jsx(M0,{});case"system":return o.jsx(R0,{});case"analysis":return o.jsx(Rd,{});case"workflow":return o.jsx(Od,{});default:return o.jsx(qa,{})}};return o.jsx("div",{className:"flex-1 flex flex-col bg-gray-50 dark:bg-dark-900 overflow-hidden",children:n()})}function A0(){const{connected:e}=Tn(),{metrics:t,alerts:n}=Pn(),{sessions:r,activeSessionId:s}=Mn(),{providers:i,selectedProvider:l}=ft(),a=r.find(c=>c.id===s),u=i.find(c=>c.id===l);return o.jsx("div",{className:"bg-nexus-600 text-white px-4 py-2 text-sm",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-6",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:`w-2 h-2 rounded-full ${e?"bg-green-400":"bg-red-400"}`}),o.jsx("span",{children:e?"Connected":"Disconnected"})]}),a&&o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 9l3 3-3 3m5 0h3"})}),o.jsx("span",{children:a.name}),o.jsxs("span",{className:"text-nexus-200",children:["(",a.blocks.length," commands)"]})]}),u&&o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})}),o.jsx("span",{children:u.name})]})]}),n.length>0&&o.jsxs("div",{className:"flex items-center space-x-2 bg-yellow-600 px-3 py-1 rounded",children:[o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})}),o.jsx("span",{children:n[0]}),n.length>1&&o.jsxs("span",{className:"bg-yellow-700 px-2 py-0.5 rounded text-xs",children:["+",n.length-1," more"]})]}),o.jsxs("div",{className:"flex items-center space-x-6",children:[t&&o.jsxs("div",{className:"flex items-center space-x-4",children:[o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"})}),o.jsxs("span",{children:["CPU: ",t.cpu.usage,"%"]})]}),o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})}),o.jsxs("span",{children:["RAM: ",t.memory.percentage,"%"]})]}),t.cpu.temperature>0&&o.jsxs("div",{className:"flex items-center space-x-1",children:[o.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:o.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),o.jsxs("span",{children:[t.cpu.temperature,"°C"]})]})]}),o.jsx("div",{className:"text-nexus-200",children:new Date().toLocaleTimeString()})]})]})})}function z0(){const[e,t]=k.useState(!1),[n,r]=k.useState("terminal");return o.jsx(dm,{children:o.jsx(cm,{children:o.jsx(hm,{children:o.jsx(fm,{children:o.jsx(pm,{children:o.jsx(mm,{children:o.jsxs("div",{className:"h-screen flex flex-col bg-gray-50 dark:bg-dark-900",children:[o.jsx(gm,{onToggleSidebar:()=>t(!e),sidebarCollapsed:e}),o.jsxs("div",{className:"flex-1 flex overflow-hidden",children:[o.jsx(xm,{collapsed:e,activePanel:n,onPanelChange:r}),o.jsx(O0,{activePanel:n,sidebarCollapsed:e})]}),o.jsx(A0,{})]})})})})})})})}Oi.createRoot(document.getElementById("root")).render(o.jsx(wt.StrictMode,{children:o.jsx(z0,{})}));setTimeout(()=>{window.dispatchEvent(new CustomEvent("nexus-ready"))},1e3);
