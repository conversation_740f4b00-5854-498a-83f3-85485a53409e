"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemMonitor = void 0;
const si = __importStar(require("systeminformation"));
const events_1 = require("events");
class SystemMonitor extends events_1.EventEmitter {
    constructor() {
        super();
        this.isRunning = false;
        this.interval = null;
        this.currentMetrics = null;
        this.metricsHistory = [];
        this.maxHistorySize = 1000; // Keep last 1000 readings
        this.updateInterval = parseInt(process.env.MONITORING_INTERVAL || '5000');
    }
    start() {
        if (this.isRunning) {
            console.log('System monitor is already running');
            return;
        }
        this.isRunning = true;
        console.log(`🖥️ Starting system monitor (interval: ${this.updateInterval}ms)`);
        // Initial reading
        this.collectMetrics();
        // Set up periodic collection
        this.interval = setInterval(() => {
            this.collectMetrics();
        }, this.updateInterval);
    }
    stop() {
        if (!this.isRunning) {
            return;
        }
        this.isRunning = false;
        if (this.interval) {
            clearInterval(this.interval);
            this.interval = null;
        }
        console.log('🛑 System monitor stopped');
    }
    async collectMetrics() {
        try {
            const [cpu, memory, disk, networkStats] = await Promise.all([
                si.cpu(),
                si.mem(),
                si.fsSize(),
                si.networkStats(),
            ]);
            // Get current CPU load
            const cpuLoad = await si.currentLoad();
            // Get CPU temperature (may not be available on all systems)
            let cpuTemp = 0;
            try {
                const temp = await si.cpuTemperature();
                cpuTemp = temp.main || 0;
            }
            catch (error) {
                // Temperature monitoring not available
            }
            // Calculate network metrics
            const networkInterface = networkStats[0] || { rx_bytes: 0, tx_bytes: 0 };
            const networkMetrics = this.calculateNetworkMetrics(networkInterface);
            // Get primary disk
            const primaryDisk = disk[0] || { size: 0, used: 0, available: 0 };
            const metrics = {
                cpu: {
                    usage: Math.round(cpuLoad.currentLoad || 0),
                    temperature: Math.round(cpuTemp),
                    cores: cpu.cores || 0,
                },
                memory: {
                    total: memory.total,
                    used: memory.used,
                    free: memory.free,
                    percentage: Math.round((memory.used / memory.total) * 100),
                },
                disk: {
                    total: primaryDisk.size,
                    used: primaryDisk.used,
                    free: primaryDisk.available,
                    percentage: Math.round((primaryDisk.used / primaryDisk.size) * 100),
                },
                network: networkMetrics,
                timestamp: Date.now(),
            };
            this.currentMetrics = metrics;
            this.addToHistory(metrics);
            // Emit metrics update
            this.emit('metrics', metrics);
            // Check for alerts
            this.checkAlerts(metrics);
        }
        catch (error) {
            console.error('Error collecting system metrics:', error);
            this.emit('error', error);
        }
    }
    calculateNetworkMetrics(networkInterface) {
        // For now, return basic metrics
        // In a real implementation, you'd calculate rates based on previous readings
        return {
            upload: 0, // bytes/sec
            download: 0, // bytes/sec
            latency: 0, // ms
        };
    }
    addToHistory(metrics) {
        this.metricsHistory.push(metrics);
        // Keep only the last N readings
        if (this.metricsHistory.length > this.maxHistorySize) {
            this.metricsHistory = this.metricsHistory.slice(-this.maxHistorySize);
        }
    }
    checkAlerts(metrics) {
        const alerts = [];
        // CPU alerts
        if (metrics.cpu.usage > 90) {
            alerts.push(`High CPU usage: ${metrics.cpu.usage}%`);
        }
        if (metrics.cpu.temperature > 80) {
            alerts.push(`High CPU temperature: ${metrics.cpu.temperature}°C`);
        }
        // Memory alerts
        if (metrics.memory.percentage > 90) {
            alerts.push(`High memory usage: ${metrics.memory.percentage}%`);
        }
        // Disk alerts
        if (metrics.disk.percentage > 90) {
            alerts.push(`Low disk space: ${metrics.disk.percentage}% used`);
        }
        // Emit alerts if any
        if (alerts.length > 0) {
            this.emit('alerts', alerts);
        }
    }
    getCurrentMetrics() {
        return this.currentMetrics;
    }
    getMetricsHistory(limit) {
        if (limit) {
            return this.metricsHistory.slice(-limit);
        }
        return [...this.metricsHistory];
    }
    getAverageMetrics(timeRangeMs) {
        const cutoffTime = Date.now() - timeRangeMs;
        const relevantMetrics = this.metricsHistory.filter(m => m.timestamp >= cutoffTime);
        if (relevantMetrics.length === 0) {
            return null;
        }
        const avgCpuUsage = relevantMetrics.reduce((sum, m) => sum + m.cpu.usage, 0) / relevantMetrics.length;
        const avgMemoryUsage = relevantMetrics.reduce((sum, m) => sum + m.memory.percentage, 0) / relevantMetrics.length;
        const avgDiskUsage = relevantMetrics.reduce((sum, m) => sum + m.disk.percentage, 0) / relevantMetrics.length;
        return {
            cpu: {
                usage: Math.round(avgCpuUsage),
                temperature: 0,
                cores: relevantMetrics[0].cpu.cores,
            },
            memory: {
                total: relevantMetrics[0].memory.total,
                used: 0,
                free: 0,
                percentage: Math.round(avgMemoryUsage),
            },
            disk: {
                total: relevantMetrics[0].disk.total,
                used: 0,
                free: 0,
                percentage: Math.round(avgDiskUsage),
            },
            network: {
                upload: 0,
                download: 0,
                latency: 0,
            },
            timestamp: Date.now(),
        };
    }
    async getProcessList() {
        try {
            const processes = await si.processes();
            return processes.list
                .sort((a, b) => (b.cpu || 0) - (a.cpu || 0))
                .slice(0, 20) // Top 20 processes by CPU usage
                .map(proc => ({
                pid: proc.pid,
                name: proc.name,
                cpu: proc.cpu || 0,
                memory: proc.mem || 0,
                command: proc.command,
            }));
        }
        catch (error) {
            console.error('Error getting process list:', error);
            return [];
        }
    }
    async getSystemInfo() {
        try {
            const [system, osInfo, cpu, graphics] = await Promise.all([
                si.system(),
                si.osInfo(),
                si.cpu(),
                si.graphics(),
            ]);
            return {
                system: {
                    manufacturer: system.manufacturer,
                    model: system.model,
                    version: system.version,
                },
                os: {
                    platform: osInfo.platform,
                    distro: osInfo.distro,
                    release: osInfo.release,
                    arch: osInfo.arch,
                },
                cpu: {
                    manufacturer: cpu.manufacturer,
                    brand: cpu.brand,
                    speed: cpu.speed,
                    cores: cpu.cores,
                    physicalCores: cpu.physicalCores,
                },
                graphics: graphics.controllers.map(gpu => ({
                    model: gpu.model,
                    vendor: gpu.vendor,
                    vram: gpu.vram,
                })),
            };
        }
        catch (error) {
            console.error('Error getting system info:', error);
            return null;
        }
    }
    isMonitoring() {
        return this.isRunning;
    }
}
exports.SystemMonitor = SystemMonitor;
