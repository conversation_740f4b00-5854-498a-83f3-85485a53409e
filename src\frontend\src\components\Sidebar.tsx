import React from 'react';
import { useSystem } from '../contexts/SystemContext';
import { useTerminal } from '../contexts/TerminalContext';
import { useFileSystem } from '../contexts/FileSystemContext';
import FileExplorer from './FileExplorer';
import CodeAnalysis from './CodeAnalysis';
import WorkflowAutomation from './WorkflowAutomation';

interface SidebarProps {
  collapsed: boolean;
  activePanel: 'terminal' | 'ai' | 'files' | 'system' | 'analysis' | 'workflow';
  onPanelChange: (panel: 'terminal' | 'ai' | 'files' | 'system' | 'analysis' | 'workflow') => void;
}

function Sidebar({ collapsed, activePanel, onPanelChange }: SidebarProps) {
  const { metrics } = useSystem();
  const { sessions } = useTerminal();
  const { fileTree } = useFileSystem();

  const panels = [
    {
      id: 'terminal' as const,
      name: 'Terminal',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3" />
        </svg>
      ),
      badge: sessions.length > 0 ? sessions.length : undefined,
    },
    {
      id: 'ai' as const,
      name: 'AI Assistant',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      ),
    },
    {
      id: 'files' as const,
      name: 'Explorer',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 1v6" />
        </svg>
      ),
      badge: fileTree?.children?.length,
    },
    {
      id: 'system' as const,
      name: 'System',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      status: metrics ? (
        metrics.cpu.usage > 80 || metrics.memory.percentage > 80 ? 'warning' : 'ok'
      ) : undefined,
    },
    {
      id: 'analysis' as const,
      name: 'Code Analysis',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      id: 'workflow' as const,
      name: 'Workflow',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      ),
    },
  ];

  return (
    <div className={`bg-white dark:bg-dark-800 border-r border-gray-200 dark:border-dark-700 transition-all duration-300 ${
      collapsed ? 'w-16' : 'w-64'
    }`}>
      {/* Panel Navigation */}
      <div className="flex flex-col h-full">
        <div className="p-4 border-b border-gray-200 dark:border-dark-700">
          <div className="flex flex-col space-y-2">
            {panels.map((panel) => (
              <button
                key={panel.id}
                onClick={() => onPanelChange(panel.id)}
                className={`flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                  activePanel === panel.id
                    ? 'bg-nexus-100 dark:bg-nexus-900 text-nexus-700 dark:text-nexus-300'
                    : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-700'
                }`}
                title={collapsed ? panel.name : undefined}
              >
                <div className="relative">
                  {panel.icon}
                  {panel.status === 'warning' && (
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full"></div>
                  )}
                </div>
                {!collapsed && (
                  <>
                    <span className="flex-1 text-left">{panel.name}</span>
                    {panel.badge && (
                      <span className="bg-gray-200 dark:bg-dark-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded-full">
                        {panel.badge}
                      </span>
                    )}
                  </>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Panel Content */}
        <div className="flex-1 overflow-hidden">
          {!collapsed && (
            <div className="h-full">
              {activePanel === 'terminal' && <TerminalPanel />}
              {activePanel === 'ai' && <AIPanel />}
              {activePanel === 'files' && <FilesPanel />}
              {activePanel === 'system' && <SystemPanel />}
              {activePanel === 'analysis' && <CodeAnalysis />}
              {activePanel === 'workflow' && <WorkflowAutomation />}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Terminal Panel Component
function TerminalPanel() {
  const { sessions, createSession, setActiveSession, activeSessionId } = useTerminal();

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Terminal Sessions</h3>
        <button
          onClick={() => createSession()}
          className="btn btn-sm btn-primary"
          title="New Terminal"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>
      
      <div className="space-y-2">
        {sessions.map((session) => (
          <div
            key={session.id}
            onClick={() => setActiveSession(session.id)}
            className={`p-3 rounded-lg cursor-pointer transition-colors ${
              activeSessionId === session.id
                ? 'bg-nexus-100 dark:bg-nexus-900 border border-nexus-300 dark:border-nexus-700'
                : 'bg-gray-50 dark:bg-dark-700 hover:bg-gray-100 dark:hover:bg-dark-600'
            }`}
          >
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {session.name}
              </span>
              <div className={`w-2 h-2 rounded-full ${session.isActive ? 'bg-green-500' : 'bg-gray-400'}`}></div>
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              {session.blocks.length} commands
            </div>
          </div>
        ))}
        
        {sessions.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg className="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3" />
            </svg>
            <p className="text-sm">No terminal sessions</p>
            <button
              onClick={() => createSession()}
              className="text-nexus-600 dark:text-nexus-400 text-sm hover:underline mt-1"
            >
              Create one now
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// AI Panel Component
function AIPanel() {
  return (
    <div className="p-4">
      <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">AI Assistant</h3>
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        <svg className="w-8 h-8 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
        <p className="text-sm">AI chat available in main panel</p>
      </div>
    </div>
  );
}

// Files Panel Component
function FilesPanel() {
  return <FileExplorer className="h-full" />;
}

// System Panel Component
function SystemPanel() {
  const { metrics } = useSystem();

  if (!metrics) {
    return (
      <div className="p-4">
        <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">System</h3>
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <div className="animate-spin w-6 h-6 border-2 border-nexus-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p className="text-sm">Loading system metrics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-4">System</h3>
      
      <div className="space-y-4">
        {/* CPU */}
        <div className="metric-card">
          <div className="flex items-center justify-between mb-2">
            <span className="metric-label">CPU</span>
            <span className="text-sm font-mono">{metrics.cpu.usage}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className={`progress-fill ${
                metrics.cpu.usage > 80 ? 'high' : metrics.cpu.usage > 60 ? 'medium' : 'low'
              }`}
              style={{ width: `${metrics.cpu.usage}%` }}
            ></div>
          </div>
        </div>

        {/* Memory */}
        <div className="metric-card">
          <div className="flex items-center justify-between mb-2">
            <span className="metric-label">Memory</span>
            <span className="text-sm font-mono">{metrics.memory.percentage}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className={`progress-fill ${
                metrics.memory.percentage > 80 ? 'high' : metrics.memory.percentage > 60 ? 'medium' : 'low'
              }`}
              style={{ width: `${metrics.memory.percentage}%` }}
            ></div>
          </div>
        </div>

        {/* Disk */}
        <div className="metric-card">
          <div className="flex items-center justify-between mb-2">
            <span className="metric-label">Disk</span>
            <span className="text-sm font-mono">{metrics.disk.percentage}%</span>
          </div>
          <div className="progress-bar">
            <div 
              className={`progress-fill ${
                metrics.disk.percentage > 80 ? 'high' : metrics.disk.percentage > 60 ? 'medium' : 'low'
              }`}
              style={{ width: `${metrics.disk.percentage}%` }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
