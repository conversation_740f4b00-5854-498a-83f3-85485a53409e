import React, { useState, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { useFileSystem } from '../contexts/FileSystemContext';
import { useTheme } from '../contexts/ThemeContext';
import { useAI } from '../contexts/AIContext';

function FileEditor() {
  const { selectedFile, openFiles, closeFile, fileContents, updateFileContent } = useFileSystem();
  const { theme } = useTheme();
  const { sendMessage } = useAI();
  const [editorContent, setEditorContent] = useState('');
  const [isModified, setIsModified] = useState(false);
  const [language, setLanguage] = useState('typescript');

  // Load file content when selected file changes
  useEffect(() => {
    if (selectedFile && fileContents[selectedFile]) {
      setEditorContent(fileContents[selectedFile]);
      setIsModified(false);

      // Detect language from file extension
      const ext = selectedFile.split('.').pop()?.toLowerCase();
      const langMap: Record<string, string> = {
        'ts': 'typescript',
        'tsx': 'typescript',
        'js': 'javascript',
        'jsx': 'javascript',
        'py': 'python',
        'java': 'java',
        'cpp': 'cpp',
        'c': 'c',
        'cs': 'csharp',
        'php': 'php',
        'rb': 'ruby',
        'go': 'go',
        'rs': 'rust',
        'html': 'html',
        'css': 'css',
        'scss': 'scss',
        'json': 'json',
        'xml': 'xml',
        'yaml': 'yaml',
        'yml': 'yaml',
        'md': 'markdown',
        'sql': 'sql',
        'sh': 'shell',
        'bash': 'shell',
      };
      setLanguage(langMap[ext || ''] || 'plaintext');
    }
  }, [selectedFile, fileContents]);

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      setEditorContent(value);
      setIsModified(value !== (fileContents[selectedFile || ''] || ''));
    }
  };

  const handleSave = () => {
    if (selectedFile && isModified) {
      updateFileContent(selectedFile, editorContent);
      setIsModified(false);
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 's') {
      e.preventDefault();
      handleSave();
    }
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedFile, isModified, editorContent]);

  const getAICodeSuggestion = async () => {
    if (!selectedFile || !editorContent) return;

    await sendMessage(`Analyze this ${language} code and provide suggestions for improvement:\n\n${editorContent}`, {
      currentFile: selectedFile,
      openFiles,
      projectType: 'nexus-ai',
      recentCommands: [],
      systemState: null,
    });
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* File Tabs */}
      {openFiles.length > 0 && (
        <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700">
          <div className="flex overflow-x-auto">
            {openFiles.map((filePath) => (
              <div
                key={filePath}
                className={`flex items-center space-x-2 px-4 py-2 border-r border-gray-200 dark:border-dark-700 ${
                  selectedFile === filePath
                    ? 'bg-gray-50 dark:bg-dark-700'
                    : 'hover:bg-gray-50 dark:hover:bg-dark-700'
                }`}
              >
                <span className="text-sm flex items-center space-x-1">
                  <span>{filePath.split('/').pop()}</span>
                  {isModified && selectedFile === filePath && (
                    <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                  )}
                </span>
                <button
                  onClick={() => closeFile(filePath)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Editor Toolbar */}
      {selectedFile && (
        <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {selectedFile}
              </span>
              <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-dark-700 rounded">
                {language}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={getAICodeSuggestion}
                className="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                🤖 AI Analyze
              </button>
              <button
                onClick={handleSave}
                disabled={!isModified}
                className={`px-3 py-1 text-xs rounded transition-colors ${
                  isModified
                    ? 'bg-green-500 text-white hover:bg-green-600'
                    : 'bg-gray-200 dark:bg-dark-700 text-gray-500 cursor-not-allowed'
                }`}
              >
                Save {isModified && '•'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Editor Content */}
      <div className="flex-1">
        {selectedFile ? (
          <Editor
            height="100%"
            language={language}
            value={editorContent}
            onChange={handleEditorChange}
            theme={theme === 'dark' ? 'vs-dark' : 'light'}
            options={{
              minimap: { enabled: true },
              fontSize: 14,
              lineNumbers: 'on',
              roundedSelection: false,
              scrollBeyondLastLine: false,
              automaticLayout: true,
              tabSize: 2,
              insertSpaces: true,
              wordWrap: 'on',
              bracketPairColorization: { enabled: true },
              guides: {
                bracketPairs: true,
                indentation: true,
              },
              suggest: {
                showKeywords: true,
                showSnippets: true,
                showFunctions: true,
                showConstructors: true,
                showFields: true,
                showVariables: true,
                showClasses: true,
                showStructs: true,
                showInterfaces: true,
                showModules: true,
                showProperties: true,
                showEvents: true,
                showOperators: true,
                showUnits: true,
                showValues: true,
                showConstants: true,
                showEnums: true,
                showEnumMembers: true,
                showColors: true,
                showFiles: true,
                showReferences: true,
                showFolders: true,
                showTypeParameters: true,
                showIssues: true,
                showUsers: true,
              },
              quickSuggestions: {
                other: true,
                comments: true,
                strings: true,
              },
              parameterHints: {
                enabled: true,
              },
              hover: {
                enabled: true,
              },
              contextmenu: true,
              mouseWheelZoom: true,
              cursorBlinking: 'blink',
              cursorSmoothCaretAnimation: 'on',
              smoothScrolling: true,
            }}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Advanced Code Editor</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Select a file from the sidebar to start editing
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <p>✨ Features:</p>
                <p>• Syntax highlighting for 30+ languages</p>
                <p>• AI-powered code analysis</p>
                <p>• Intelligent autocomplete</p>
                <p>• Real-time error detection</p>
                <p>• Ctrl+S to save</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default FileEditor;
