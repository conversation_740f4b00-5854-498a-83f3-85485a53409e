import React, { useState, useEffect } from 'react';
import { useAI } from '../contexts/AIContext';
import { useTerminal } from '../contexts/TerminalContext';
import { useFileSystem } from '../contexts/FileSystemContext';

interface WorkflowStep {
  id: string;
  type: 'command' | 'file_operation' | 'ai_query' | 'wait';
  description: string;
  action: string;
  parameters?: any;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  frequency: number;
  lastUsed: Date;
  isAutomated: boolean;
}

interface WorkflowSuggestion {
  id: string;
  name: string;
  description: string;
  confidence: number;
  steps: string[];
  pattern: string[];
}

function WorkflowAutomation() {
  const { sendMessage } = useAI();
  const { executeCommand } = useTerminal();
  const { openFile, selectedFile } = useFileSystem();
  
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [suggestions, setSuggestions] = useState<WorkflowSuggestion[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [recordedActions, setRecordedActions] = useState<string[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);

  useEffect(() => {
    // Load saved workflows and generate suggestions
    loadWorkflows();
    generateSuggestions();
  }, []);

  const loadWorkflows = () => {
    // Mock workflows - in a real app, these would be loaded from storage
    const mockWorkflows: Workflow[] = [
      {
        id: 'setup-dev',
        name: 'Development Setup',
        description: 'Set up development environment for a new project',
        steps: [
          {
            id: '1',
            type: 'command',
            description: 'Install dependencies',
            action: 'npm install',
            status: 'pending'
          },
          {
            id: '2',
            type: 'command',
            description: 'Start development server',
            action: 'npm run dev',
            status: 'pending'
          },
          {
            id: '3',
            type: 'file_operation',
            description: 'Open main file',
            action: 'open_file',
            parameters: { file: 'src/App.tsx' },
            status: 'pending'
          }
        ],
        frequency: 15,
        lastUsed: new Date(),
        isAutomated: false
      },
      {
        id: 'git-workflow',
        name: 'Git Commit Workflow',
        description: 'Standard git workflow for committing changes',
        steps: [
          {
            id: '1',
            type: 'command',
            description: 'Check git status',
            action: 'git status',
            status: 'pending'
          },
          {
            id: '2',
            type: 'command',
            description: 'Add all changes',
            action: 'git add .',
            status: 'pending'
          },
          {
            id: '3',
            type: 'ai_query',
            description: 'Generate commit message',
            action: 'Generate a commit message for the current changes',
            status: 'pending'
          }
        ],
        frequency: 42,
        lastUsed: new Date(Date.now() - 86400000),
        isAutomated: true
      }
    ];

    setWorkflows(mockWorkflows);
  };

  const generateSuggestions = () => {
    // Mock suggestions based on user patterns
    const mockSuggestions: WorkflowSuggestion[] = [
      {
        id: 'test-workflow',
        name: 'Testing Workflow',
        description: 'You often run tests after making changes',
        confidence: 0.85,
        steps: ['Save file', 'Run tests', 'Check results'],
        pattern: ['file_save', 'npm test', 'check_output']
      },
      {
        id: 'deploy-workflow',
        name: 'Deployment Workflow',
        description: 'Automated deployment process',
        confidence: 0.72,
        steps: ['Build project', 'Run tests', 'Deploy to staging'],
        pattern: ['npm run build', 'npm test', 'deploy']
      }
    ];

    setSuggestions(mockSuggestions);
  };

  const startRecording = () => {
    setIsRecording(true);
    setRecordedActions([]);
  };

  const stopRecording = () => {
    setIsRecording(false);
    if (recordedActions.length > 0) {
      createWorkflowFromRecording();
    }
  };

  const recordAction = (action: string) => {
    if (isRecording) {
      setRecordedActions(prev => [...prev, action]);
    }
  };

  const createWorkflowFromRecording = () => {
    const workflowName = prompt('Enter a name for this workflow:');
    if (!workflowName) return;

    const newWorkflow: Workflow = {
      id: Date.now().toString(),
      name: workflowName,
      description: `Recorded workflow with ${recordedActions.length} steps`,
      steps: recordedActions.map((action, index) => ({
        id: (index + 1).toString(),
        type: action.startsWith('npm') || action.startsWith('git') ? 'command' : 'ai_query',
        description: action,
        action: action,
        status: 'pending' as const
      })),
      frequency: 1,
      lastUsed: new Date(),
      isAutomated: false
    };

    setWorkflows(prev => [...prev, newWorkflow]);
    setRecordedActions([]);
  };

  const executeWorkflow = async (workflowId: string) => {
    const workflow = workflows.find(w => w.id === workflowId);
    if (!workflow) return;

    setIsExecuting(true);
    setSelectedWorkflow(workflowId);

    try {
      for (const step of workflow.steps) {
        // Update step status to running
        setWorkflows(prev => prev.map(w => 
          w.id === workflowId 
            ? {
                ...w,
                steps: w.steps.map(s => 
                  s.id === step.id ? { ...s, status: 'running' } : s
                )
              }
            : w
        ));

        // Execute step based on type
        switch (step.type) {
          case 'command':
            // Execute terminal command
            await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate execution
            break;
          case 'file_operation':
            if (step.action === 'open_file' && step.parameters?.file) {
              openFile(step.parameters.file);
            }
            break;
          case 'ai_query':
            await sendMessage(step.action, {
              currentFile: selectedFile,
              openFiles: [],
              projectType: 'workflow-automation',
              recentCommands: [],
              systemState: null,
            });
            break;
          case 'wait':
            await new Promise(resolve => setTimeout(resolve, step.parameters?.duration || 1000));
            break;
        }

        // Update step status to completed
        setWorkflows(prev => prev.map(w => 
          w.id === workflowId 
            ? {
                ...w,
                steps: w.steps.map(s => 
                  s.id === step.id ? { ...s, status: 'completed' } : s
                )
              }
            : w
        ));
      }

      // Update workflow last used
      setWorkflows(prev => prev.map(w => 
        w.id === workflowId 
          ? { ...w, lastUsed: new Date(), frequency: w.frequency + 1 }
          : w
      ));

    } catch (error) {
      console.error('Workflow execution failed:', error);
    } finally {
      setIsExecuting(false);
      setSelectedWorkflow(null);
    }
  };

  const createWorkflowFromSuggestion = (suggestion: WorkflowSuggestion) => {
    const newWorkflow: Workflow = {
      id: suggestion.id,
      name: suggestion.name,
      description: suggestion.description,
      steps: suggestion.steps.map((step, index) => ({
        id: (index + 1).toString(),
        type: 'command',
        description: step,
        action: suggestion.pattern[index] || step,
        status: 'pending' as const
      })),
      frequency: 0,
      lastUsed: new Date(),
      isAutomated: false
    };

    setWorkflows(prev => [...prev, newWorkflow]);
    setSuggestions(prev => prev.filter(s => s.id !== suggestion.id));
  };

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'command':
        return '⚡';
      case 'file_operation':
        return '📁';
      case 'ai_query':
        return '🤖';
      case 'wait':
        return '⏱️';
      default:
        return '📝';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'running':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
      case 'failed':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Workflow Automation</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Automate repetitive tasks and learn from your patterns
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={isRecording ? stopRecording : startRecording}
              className={`btn ${isRecording ? 'btn-danger' : 'btn-primary'}`}
            >
              {isRecording ? (
                <>
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
                  Stop Recording
                </>
              ) : (
                '🎬 Record Workflow'
              )}
            </button>
          </div>
        </div>
        
        {isRecording && (
          <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-red-700 dark:text-red-300">
                Recording actions... ({recordedActions.length} steps recorded)
              </span>
            </div>
            {recordedActions.length > 0 && (
              <div className="mt-2 text-xs text-red-600 dark:text-red-400">
                Latest: {recordedActions[recordedActions.length - 1]}
              </div>
            )}
          </div>
        )}
      </div>

      <div className="flex-1 overflow-y-auto">
        {/* Suggestions */}
        {suggestions.length > 0 && (
          <div className="p-4 border-b border-gray-200 dark:border-dark-700">
            <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
              💡 Suggested Workflows
            </h3>
            <div className="space-y-3">
              {suggestions.map((suggestion) => (
                <div
                  key={suggestion.id}
                  className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-blue-900 dark:text-blue-100">
                          {suggestion.name}
                        </span>
                        <span className="text-xs px-2 py-1 bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200 rounded">
                          {Math.round(suggestion.confidence * 100)}% confidence
                        </span>
                      </div>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">
                        {suggestion.description}
                      </p>
                      <div className="text-xs text-blue-600 dark:text-blue-400">
                        Steps: {suggestion.steps.join(' → ')}
                      </div>
                    </div>
                    <button
                      onClick={() => createWorkflowFromSuggestion(suggestion)}
                      className="ml-3 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Create Workflow
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Workflows */}
        <div className="p-4">
          <h3 className="text-md font-medium text-gray-900 dark:text-white mb-3">
            🔄 Your Workflows
          </h3>
          
          {workflows.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-2">🤖</div>
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No Workflows Yet
              </h4>
              <p className="text-gray-600 dark:text-gray-400">
                Start recording your actions to create automated workflows
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {workflows.map((workflow) => (
                <div
                  key={workflow.id}
                  className="bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-700 rounded-lg p-4"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-gray-900 dark:text-white">
                          {workflow.name}
                        </span>
                        {workflow.isAutomated && (
                          <span className="text-xs px-2 py-1 bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200 rounded">
                            Automated
                          </span>
                        )}
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          Used {workflow.frequency} times
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {workflow.description}
                      </p>
                    </div>
                    <button
                      onClick={() => executeWorkflow(workflow.id)}
                      disabled={isExecuting && selectedWorkflow === workflow.id}
                      className="btn btn-primary"
                    >
                      {isExecuting && selectedWorkflow === workflow.id ? (
                        <div className="flex items-center space-x-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>Running...</span>
                        </div>
                      ) : (
                        '▶️ Execute'
                      )}
                    </button>
                  </div>

                  {/* Workflow Steps */}
                  <div className="space-y-2">
                    {workflow.steps.map((step, index) => (
                      <div
                        key={step.id}
                        className="flex items-center space-x-3 p-2 bg-gray-50 dark:bg-dark-700 rounded"
                      >
                        <span className="text-lg">{getStepIcon(step.type)}</span>
                        <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                          {index + 1}.
                        </span>
                        <span className="flex-1 text-sm text-gray-900 dark:text-white">
                          {step.description}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded ${getStatusColor(step.status)}`}>
                          {step.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default WorkflowAutomation;
