import React, { useState, useEffect, useRef } from 'react';
import { useAI } from '../contexts/AIContext';
import { useTerminal } from '../contexts/TerminalContext';
import { useFileSystem } from '../contexts/FileSystemContext';

interface VoiceCommandProps {
  onCommand?: (command: string) => void;
}

interface SpeechRecognitionEvent extends Event {
  results: SpeechRecognitionResultList;
  resultIndex: number;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

function VoiceCommand({ onCommand }: VoiceCommandProps) {
  const [isListening, setIsListening] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  const { sendMessage } = useAI();
  const { executeCommand } = useTerminal();
  const { openFile, searchFiles } = useFileSystem();
  
  const recognitionRef = useRef<any>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    
    if (SpeechRecognition) {
      setIsSupported(true);
      
      const recognition = new SpeechRecognition();
      recognition.continuous = false;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        setIsListening(true);
        setError(null);
        setTranscript('');
      };

      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            setConfidence(result[0].confidence);
          } else {
            interimTranscript += result[0].transcript;
          }
        }

        setTranscript(finalTranscript || interimTranscript);

        if (finalTranscript) {
          processVoiceCommand(finalTranscript.trim());
        }
      };

      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        setError(`Speech recognition error: ${event.error}`);
        setIsListening(false);
      };

      recognition.onend = () => {
        setIsListening(false);
      };

      recognitionRef.current = recognition;
    } else {
      setIsSupported(false);
      setError('Speech recognition is not supported in this browser');
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      try {
        recognitionRef.current.start();
        
        // Auto-stop after 10 seconds
        timeoutRef.current = setTimeout(() => {
          stopListening();
        }, 10000);
      } catch (error) {
        setError('Failed to start speech recognition');
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
  };

  const processVoiceCommand = async (command: string) => {
    console.log('Processing voice command:', command);
    
    // Call the onCommand callback if provided
    if (onCommand) {
      onCommand(command);
    }

    // Parse and execute the command
    const lowerCommand = command.toLowerCase();
    
    try {
      // File operations
      if (lowerCommand.includes('open file') || lowerCommand.includes('open the file')) {
        const fileName = extractFileName(command);
        if (fileName) {
          const files = await searchFiles(fileName);
          if (files.length > 0) {
            openFile(files[0]);
            setTranscript(`Opening ${files[0]}`);
          } else {
            setTranscript(`File "${fileName}" not found`);
          }
        }
        return;
      }

      // Terminal commands
      if (lowerCommand.includes('run') || lowerCommand.includes('execute')) {
        const terminalCommand = extractTerminalCommand(command);
        if (terminalCommand) {
          executeCommand(terminalCommand);
          setTranscript(`Executing: ${terminalCommand}`);
        }
        return;
      }

      // AI queries
      if (lowerCommand.includes('ai') || lowerCommand.includes('help') || lowerCommand.includes('explain')) {
        const aiQuery = command.replace(/^(ai|help|explain)\s*/i, '');
        await sendMessage(aiQuery, {
          currentFile: null,
          openFiles: [],
          projectType: 'voice-command',
          recentCommands: [],
          systemState: null,
        });
        setTranscript(`AI processing: ${aiQuery}`);
        return;
      }

      // Navigation commands
      if (lowerCommand.includes('go to') || lowerCommand.includes('navigate to')) {
        const target = extractNavigationTarget(command);
        if (target) {
          // This would trigger navigation in the app
          setTranscript(`Navigating to ${target}`);
        }
        return;
      }

      // Default: treat as AI query
      await sendMessage(command, {
        currentFile: null,
        openFiles: [],
        projectType: 'voice-command',
        recentCommands: [],
        systemState: null,
      });
      setTranscript(`Processing: ${command}`);

    } catch (error) {
      console.error('Error processing voice command:', error);
      setError('Failed to process voice command');
    }
  };

  const extractFileName = (command: string): string | null => {
    // Extract file name from commands like "open file app.tsx" or "open the file app.tsx"
    const match = command.match(/open (?:the )?file (.+)/i);
    return match ? match[1].trim() : null;
  };

  const extractTerminalCommand = (command: string): string | null => {
    // Extract terminal command from "run npm install" or "execute git status"
    const match = command.match(/(?:run|execute) (.+)/i);
    return match ? match[1].trim() : null;
  };

  const extractNavigationTarget = (command: string): string | null => {
    // Extract navigation target from "go to terminal" or "navigate to files"
    const match = command.match(/(?:go to|navigate to) (.+)/i);
    return match ? match[1].trim() : null;
  };

  if (!isSupported) {
    return (
      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <span className="text-sm text-yellow-800 dark:text-yellow-200">
            Voice commands are not supported in this browser
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white dark:bg-dark-800 border border-gray-200 dark:border-dark-700 rounded-lg">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Voice Commands</h3>
        <div className="flex items-center space-x-2">
          {confidence > 0 && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Confidence: {Math.round(confidence * 100)}%
            </span>
          )}
          <button
            onClick={isListening ? stopListening : startListening}
            className={`p-2 rounded-full transition-colors ${
              isListening
                ? 'bg-red-500 text-white hover:bg-red-600'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
            title={isListening ? 'Stop listening' : 'Start voice command'}
          >
            {isListening ? (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 10a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Status */}
      <div className="mb-4">
        {isListening && (
          <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-sm">Listening...</span>
          </div>
        )}
        
        {transcript && (
          <div className="mt-2 p-3 bg-gray-50 dark:bg-dark-700 rounded border">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              <strong>Transcript:</strong> {transcript}
            </p>
          </div>
        )}
        
        {error && (
          <div className="mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
            <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}
      </div>

      {/* Command Examples */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        <p className="font-medium mb-2">Try saying:</p>
        <ul className="space-y-1 text-xs">
          <li>• "Open file App.tsx"</li>
          <li>• "Run npm install"</li>
          <li>• "AI help me debug this code"</li>
          <li>• "Go to terminal"</li>
          <li>• "Explain this function"</li>
        </ul>
      </div>
    </div>
  );
}

export default VoiceCommand;
