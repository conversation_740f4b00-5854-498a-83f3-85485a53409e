"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSystemService = void 0;
const fs = __importStar(require("fs/promises"));
const path = __importStar(require("path"));
const chokidar_1 = require("chokidar");
const simple_git_1 = require("simple-git");
const events_1 = require("events");
class FileSystemService extends events_1.EventEmitter {
    constructor() {
        super();
        this.watchers = new Map();
        this.currentProject = null;
        this.git = (0, simple_git_1.simpleGit)();
    }
    async getDirectoryTree(rootPath, maxDepth = 3) {
        try {
            const stats = await fs.stat(rootPath);
            const node = {
                name: path.basename(rootPath),
                path: rootPath,
                type: stats.isDirectory() ? 'directory' : 'file',
                size: stats.isFile() ? stats.size : undefined,
                lastModified: stats.mtime.getTime(),
            };
            if (stats.isDirectory() && maxDepth > 0) {
                try {
                    const entries = await fs.readdir(rootPath);
                    const children = [];
                    for (const entry of entries) {
                        // Skip hidden files and common ignore patterns
                        if (this.shouldIgnoreFile(entry))
                            continue;
                        const entryPath = path.join(rootPath, entry);
                        try {
                            const childNode = await this.getDirectoryTree(entryPath, maxDepth - 1);
                            // Add git status if available
                            childNode.gitStatus = await this.getGitStatus(entryPath);
                            children.push(childNode);
                        }
                        catch (error) {
                            // Skip files that can't be accessed
                            console.warn(`Skipping ${entryPath}: ${error}`);
                        }
                    }
                    // Sort: directories first, then files, both alphabetically
                    children.sort((a, b) => {
                        if (a.type !== b.type) {
                            return a.type === 'directory' ? -1 : 1;
                        }
                        return a.name.localeCompare(b.name);
                    });
                    node.children = children;
                }
                catch (error) {
                    console.warn(`Cannot read directory ${rootPath}: ${error}`);
                }
            }
            return node;
        }
        catch (error) {
            throw new Error(`Cannot access ${rootPath}: ${error}`);
        }
    }
    shouldIgnoreFile(filename) {
        const ignorePatterns = [
            /^\./, // Hidden files
            /^node_modules$/, // Node modules
            /^\.git$/, // Git directory
            /^dist$/, // Build output
            /^build$/, // Build output
            /^coverage$/, // Test coverage
            /^\.next$/, // Next.js
            /^\.nuxt$/, // Nuxt.js
            /^\.vscode$/, // VS Code
            /^\.idea$/, // IntelliJ
            /^__pycache__$/, // Python cache
            /^\.pytest_cache$/, // Pytest cache
            /^target$/, // Rust/Java build
            /^vendor$/, // Go/PHP dependencies
        ];
        return ignorePatterns.some(pattern => pattern.test(filename));
    }
    async getGitStatus(filePath) {
        try {
            const status = await this.git.status();
            const relativePath = path.relative(process.cwd(), filePath);
            if (status.modified.includes(relativePath))
                return 'modified';
            if (status.created.includes(relativePath))
                return 'added';
            if (status.deleted.includes(relativePath))
                return 'deleted';
            if (status.not_added.includes(relativePath))
                return 'untracked';
            if (status.staged.includes(relativePath))
                return 'staged';
            return undefined;
        }
        catch (error) {
            // Git not available or not a git repository
            return undefined;
        }
    }
    async readFile(filePath) {
        try {
            return await fs.readFile(filePath, 'utf-8');
        }
        catch (error) {
            throw new Error(`Cannot read file ${filePath}: ${error}`);
        }
    }
    async writeFile(filePath, content) {
        try {
            // Ensure directory exists
            const dir = path.dirname(filePath);
            await fs.mkdir(dir, { recursive: true });
            await fs.writeFile(filePath, content, 'utf-8');
            this.emit('fileChanged', { path: filePath, type: 'modified' });
        }
        catch (error) {
            throw new Error(`Cannot write file ${filePath}: ${error}`);
        }
    }
    async createDirectory(dirPath) {
        try {
            await fs.mkdir(dirPath, { recursive: true });
            this.emit('fileChanged', { path: dirPath, type: 'added' });
        }
        catch (error) {
            throw new Error(`Cannot create directory ${dirPath}: ${error}`);
        }
    }
    async deleteFile(filePath) {
        try {
            const stats = await fs.stat(filePath);
            if (stats.isDirectory()) {
                await fs.rmdir(filePath, { recursive: true });
            }
            else {
                await fs.unlink(filePath);
            }
            this.emit('fileChanged', { path: filePath, type: 'deleted' });
        }
        catch (error) {
            throw new Error(`Cannot delete ${filePath}: ${error}`);
        }
    }
    async renameFile(oldPath, newPath) {
        try {
            await fs.rename(oldPath, newPath);
            this.emit('fileChanged', { path: oldPath, type: 'deleted' });
            this.emit('fileChanged', { path: newPath, type: 'added' });
        }
        catch (error) {
            throw new Error(`Cannot rename ${oldPath} to ${newPath}: ${error}`);
        }
    }
    watchDirectory(dirPath) {
        if (this.watchers.has(dirPath)) {
            return; // Already watching
        }
        const watcher = (0, chokidar_1.watch)(dirPath, {
            ignored: /(^|[\/\\])\../, // Ignore hidden files
            persistent: true,
            ignoreInitial: true,
        });
        watcher
            .on('add', (filePath) => {
            this.emit('fileChanged', { path: filePath, type: 'added' });
        })
            .on('change', (filePath) => {
            this.emit('fileChanged', { path: filePath, type: 'modified' });
        })
            .on('unlink', (filePath) => {
            this.emit('fileChanged', { path: filePath, type: 'deleted' });
        })
            .on('addDir', (dirPath) => {
            this.emit('fileChanged', { path: dirPath, type: 'added' });
        })
            .on('unlinkDir', (dirPath) => {
            this.emit('fileChanged', { path: dirPath, type: 'deleted' });
        })
            .on('error', (error) => {
            console.error(`File watcher error for ${dirPath}:`, error);
        });
        this.watchers.set(dirPath, watcher);
        console.log(`👁️ Watching directory: ${dirPath}`);
    }
    stopWatching(dirPath) {
        const watcher = this.watchers.get(dirPath);
        if (watcher) {
            watcher.close();
            this.watchers.delete(dirPath);
            console.log(`👁️ Stopped watching directory: ${dirPath}`);
        }
    }
    async analyzeProject(rootPath) {
        try {
            const packageJsonPath = path.join(rootPath, 'package.json');
            const cargoTomlPath = path.join(rootPath, 'Cargo.toml');
            const requirementsPath = path.join(rootPath, 'requirements.txt');
            const goModPath = path.join(rootPath, 'go.mod');
            let projectType = 'unknown';
            let language = 'unknown';
            let framework;
            let dependencies = [];
            let scripts = {};
            // Check for Node.js project
            try {
                const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
                projectType = 'nodejs';
                language = 'javascript';
                if (packageJson.dependencies) {
                    dependencies = Object.keys(packageJson.dependencies);
                    // Detect framework
                    if (dependencies.includes('react'))
                        framework = 'react';
                    else if (dependencies.includes('vue'))
                        framework = 'vue';
                    else if (dependencies.includes('angular'))
                        framework = 'angular';
                    else if (dependencies.includes('next'))
                        framework = 'nextjs';
                    else if (dependencies.includes('nuxt'))
                        framework = 'nuxtjs';
                    else if (dependencies.includes('express'))
                        framework = 'express';
                }
                if (packageJson.scripts) {
                    scripts = packageJson.scripts;
                }
                // Check for TypeScript
                if (dependencies.includes('typescript') || dependencies.includes('@types/node')) {
                    language = 'typescript';
                }
            }
            catch (error) {
                // Not a Node.js project
            }
            // Check for Rust project
            try {
                await fs.access(cargoTomlPath);
                projectType = 'rust';
                language = 'rust';
            }
            catch (error) {
                // Not a Rust project
            }
            // Check for Python project
            try {
                await fs.access(requirementsPath);
                projectType = 'python';
                language = 'python';
                const requirements = await fs.readFile(requirementsPath, 'utf-8');
                dependencies = requirements.split('\n')
                    .filter(line => line.trim() && !line.startsWith('#'))
                    .map(line => line.split('==')[0].split('>=')[0].split('<=')[0].trim());
            }
            catch (error) {
                // Not a Python project
            }
            // Check for Go project
            try {
                await fs.access(goModPath);
                projectType = 'go';
                language = 'go';
            }
            catch (error) {
                // Not a Go project
            }
            // Get git remote
            let gitRemote;
            try {
                const remotes = await this.git.getRemotes(true);
                gitRemote = remotes.find(r => r.name === 'origin')?.refs?.fetch;
            }
            catch (error) {
                // No git or no remote
            }
            const project = {
                name: path.basename(rootPath),
                path: rootPath,
                type: projectType,
                language,
                framework,
                dependencies,
                scripts,
                gitRemote,
                lastActivity: Date.now(),
            };
            this.currentProject = project;
            return project;
        }
        catch (error) {
            throw new Error(`Cannot analyze project ${rootPath}: ${error}`);
        }
    }
    getCurrentProject() {
        return this.currentProject;
    }
    async searchFiles(rootPath, query, extensions) {
        const results = [];
        const searchInDirectory = async (dirPath, depth = 0) => {
            if (depth > 10)
                return; // Prevent infinite recursion
            try {
                const entries = await fs.readdir(dirPath);
                for (const entry of entries) {
                    if (this.shouldIgnoreFile(entry))
                        continue;
                    const entryPath = path.join(dirPath, entry);
                    const stats = await fs.stat(entryPath);
                    if (stats.isDirectory()) {
                        await searchInDirectory(entryPath, depth + 1);
                    }
                    else if (stats.isFile()) {
                        // Check extension filter
                        if (extensions && extensions.length > 0) {
                            const ext = path.extname(entry).toLowerCase();
                            if (!extensions.includes(ext))
                                continue;
                        }
                        // Check filename match
                        if (entry.toLowerCase().includes(query.toLowerCase())) {
                            results.push(entryPath);
                        }
                    }
                }
            }
            catch (error) {
                // Skip directories that can't be accessed
            }
        };
        await searchInDirectory(rootPath);
        return results;
    }
    cleanup() {
        console.log('🧹 Cleaning up file system watchers...');
        for (const [dirPath, watcher] of this.watchers) {
            try {
                watcher.close();
            }
            catch (error) {
                console.error(`Error closing watcher for ${dirPath}:`, error);
            }
        }
        this.watchers.clear();
    }
}
exports.FileSystemService = FileSystemService;
