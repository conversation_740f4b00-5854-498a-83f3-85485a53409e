import React, { useState, useRef, useEffect } from 'react';
import { useAI } from '../contexts/AIContext';
import { useSystem } from '../contexts/SystemContext';
import { useFileSystem } from '../contexts/FileSystemContext';
import VoiceCommand from './VoiceCommand';

function AIChat() {
  const { 
    providers, 
    selectedProvider, 
    setSelectedProvider, 
    chatHistory, 
    isLoading, 
    sendMessage, 
    clearHistory 
  } = useAI();
  const { metrics } = useSystem();
  const { selectedFile, openFiles } = useFileSystem();
  const [inputMessage, setInputMessage] = useState('');
  const [showVoiceCommand, setShowVoiceCommand] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory]);

  useEffect(() => {
    // Focus input when component mounts
    inputRef.current?.focus();
  }, []);

  useEffect(() => {
    // Generate intelligent suggestions based on context
    const generateSuggestions = () => {
      const contextSuggestions: string[] = [];

      if (selectedFile) {
        contextSuggestions.push(`Analyze ${selectedFile.split('/').pop()}`);
        contextSuggestions.push(`Explain this code in ${selectedFile}`);
        contextSuggestions.push(`Find bugs in ${selectedFile}`);
        contextSuggestions.push(`Optimize ${selectedFile}`);
      }

      if (openFiles.length > 1) {
        contextSuggestions.push('Compare these open files');
        contextSuggestions.push('Refactor common code across files');
      }

      if (metrics?.memory.percentage > 80) {
        contextSuggestions.push('Help optimize memory usage');
      }

      if (metrics?.cpu.usage > 80) {
        contextSuggestions.push('Analyze high CPU usage');
      }

      // Add general suggestions
      contextSuggestions.push('Generate unit tests');
      contextSuggestions.push('Review code quality');
      contextSuggestions.push('Suggest improvements');

      setSuggestions(contextSuggestions.slice(0, 4));
    };

    generateSuggestions();
  }, [selectedFile, openFiles, metrics]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inputMessage.trim() || isLoading) return;

    const message = inputMessage.trim();
    setInputMessage('');

    // Build context for AI
    const context = {
      currentFile: selectedFile,
      openFiles: openFiles,
      projectType: 'nexus-ai',
      recentCommands: [],
      systemState: metrics || {
        cpu: { usage: 0, temperature: 0, cores: 0 },
        memory: { total: 0, used: 0, free: 0, percentage: 0 },
        disk: { total: 0, used: 0, free: 0, percentage: 0 },
        network: { upload: 0, download: 0, latency: 0 },
        timestamp: Date.now(),
      },
    };

    await sendMessage(message, context);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const renderMessage = (message: any) => {
    const isUser = message.type === 'user';
    const isSystem = message.type === 'system';

    return (
      <div key={message.id} className={`mb-6 ${isUser ? 'ml-8' : 'mr-8'}`}>
        <div className={`ai-chat-message ${message.type}`}>
          {/* Message Header */}
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                isUser ? 'bg-nexus-600 text-white' : 
                isSystem ? 'bg-yellow-600 text-white' :
                'bg-gray-600 text-white'
              }`}>
                {isUser ? 'U' : isSystem ? 'S' : 'AI'}
              </div>
              <span className="text-sm font-medium">
                {isUser ? 'You' : isSystem ? 'System' : 'NEXUS AI'}
              </span>
              {message.metadata?.provider && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  via {providers.find(p => p.id === message.metadata.provider)?.name}
                </span>
              )}
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {formatTimestamp(message.timestamp)}
            </span>
          </div>

          {/* Message Content */}
          <div className="prose prose-sm dark:prose-invert max-w-none">
            <div className="whitespace-pre-wrap">{message.content}</div>
          </div>

          {/* Message Metadata */}
          {message.metadata && !isUser && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-4">
                  {message.metadata.confidence && (
                    <span>Confidence: {Math.round(message.metadata.confidence * 100)}%</span>
                  )}
                  {message.metadata.provider && (
                    <span>Provider: {message.metadata.provider}</span>
                  )}
                </div>
              </div>
              
              {/* Suggestions */}
              {message.metadata.suggestions && message.metadata.suggestions.length > 0 && (
                <div className="mt-2">
                  <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">Suggestions:</div>
                  <div className="flex flex-wrap gap-1">
                    {message.metadata.suggestions.map((suggestion: string, index: number) => (
                      <button
                        key={index}
                        onClick={() => setInputMessage(suggestion)}
                        className="px-2 py-1 bg-gray-200 dark:bg-gray-700 text-xs rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="flex-1 flex flex-col">
      {/* AI Chat Header */}
      <div className="bg-white dark:bg-dark-800 border-b border-gray-200 dark:border-dark-700 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-nexus-500 to-nexus-700 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">NEXUS AI Assistant</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">Your intelligent development companion</p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {/* Provider Selection */}
            <select
              value={selectedProvider || ''}
              onChange={(e) => setSelectedProvider(e.target.value || null)}
              className="input text-sm"
            >
              <option value="">Auto-select provider</option>
              {providers.filter(p => p.isAvailable).map(provider => (
                <option key={provider.id} value={provider.id}>
                  {provider.name}
                </option>
              ))}
            </select>

            {/* Clear Chat */}
            <button
              onClick={clearHistory}
              className="btn btn-sm btn-secondary"
              title="Clear chat history"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
        {chatHistory.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-gradient-to-br from-nexus-500 to-nexus-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Welcome to NEXUS AI
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                I'm your intelligent development assistant. I can help you with coding, debugging, 
                system optimization, and much more. Ask me anything!
              </p>
              <div className="grid grid-cols-1 gap-2 text-sm">
                <button
                  onClick={() => setInputMessage("What can you help me with?")}
                  className="p-3 bg-gray-100 dark:bg-dark-700 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors text-left"
                >
                  💡 What can you help me with?
                </button>
                <button
                  onClick={() => setInputMessage("Analyze my system performance")}
                  className="p-3 bg-gray-100 dark:bg-dark-700 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors text-left"
                >
                  📊 Analyze my system performance
                </button>
                <button
                  onClick={() => setInputMessage("Help me optimize my development workflow")}
                  className="p-3 bg-gray-100 dark:bg-dark-700 rounded-lg hover:bg-gray-200 dark:hover:bg-dark-600 transition-colors text-left"
                >
                  ⚡ Help me optimize my development workflow
                </button>
              </div>
            </div>
          </div>
        ) : (
          <>
            {chatHistory.map(renderMessage)}
            {isLoading && (
              <div className="mr-8 mb-6">
                <div className="ai-chat-message assistant">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-6 h-6 rounded-full bg-gray-600 flex items-center justify-center text-xs font-bold text-white">
                      AI
                    </div>
                    <span className="text-sm font-medium">NEXUS AI</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">thinking...</span>
                  </div>
                  <div className="typing-indicator">
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Voice Command Panel */}
      {showVoiceCommand && (
        <div className="border-t border-gray-200 dark:border-dark-700">
          <VoiceCommand onCommand={(command) => {
            setInputMessage(command);
            setShowVoiceCommand(false);
          }} />
        </div>
      )}

      {/* Intelligent Suggestions */}
      {suggestions.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800 p-3">
          <div className="flex items-center space-x-2 mb-2">
            <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            <span className="text-sm font-medium text-blue-700 dark:text-blue-300">AI Suggestions</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => {
                  setInputMessage(suggestion);
                  setSuggestions([]);
                }}
                className="px-3 py-1 text-sm bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded-full hover:bg-blue-200 dark:hover:bg-blue-700 transition-colors"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Chat Input */}
      <div className="bg-white dark:bg-dark-800 border-t border-gray-200 dark:border-dark-700 p-4">
        <form onSubmit={handleSubmit} className="flex space-x-3">
          <div className="flex-1 relative">
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ask NEXUS AI anything... (Shift+Enter for new line)"
              className="input resize-none pr-12"
              rows={1}
              disabled={isLoading}
            />
            <button
              type="button"
              onClick={() => setShowVoiceCommand(!showVoiceCommand)}
              className="absolute right-2 top-2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
              title="Voice Command"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
            </button>
          </div>
          <button
            type="submit"
            disabled={!inputMessage.trim() || isLoading}
            className="btn btn-primary px-6"
          >
            {isLoading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            )}
          </button>
        </form>
        
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-4">
            <span>
              Provider: {selectedProvider ? 
                providers.find(p => p.id === selectedProvider)?.name || 'Unknown' : 
                'Auto-select'
              }
            </span>
            <span>Messages: {chatHistory.length}</span>
          </div>
          <span>Press Shift+Enter for new line</span>
        </div>
      </div>
    </div>
  );
}

export default AIChat;
