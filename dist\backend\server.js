"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const cors_1 = __importDefault(require("cors"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const AIOrchestrator_1 = require("./services/AIOrchestrator");
const SystemMonitor_1 = require("./services/SystemMonitor");
const TerminalManager_1 = require("./services/TerminalManager");
const FileSystemService_1 = require("./services/FileSystemService");
const PredictiveIntelligence_1 = require("./services/PredictiveIntelligence");
const index_1 = require("./routes/index");
const index_2 = require("./socket/index");
// Load environment variables
dotenv_1.default.config();
const app = (0, express_1.default)();
const server = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env.NODE_ENV === 'production' ? false : ['http://localhost:3000'],
        methods: ['GET', 'POST'],
    },
});
const PORT = process.env.PORT || 8000;
// Middleware
app.use((0, cors_1.default)());
app.use(express_1.default.json());
app.use(express_1.default.static(path_1.default.join(__dirname, '../frontend')));
// Initialize core services
const aiOrchestrator = new AIOrchestrator_1.AIOrchestrator();
const systemMonitor = new SystemMonitor_1.SystemMonitor();
const terminalManager = new TerminalManager_1.TerminalManager();
const fileSystemService = new FileSystemService_1.FileSystemService();
const predictiveIntelligence = new PredictiveIntelligence_1.PredictiveIntelligence(aiOrchestrator, fileSystemService, systemMonitor);
// Setup routes
(0, index_1.setupRoutes)(app, {
    aiOrchestrator,
    systemMonitor,
    terminalManager,
    fileSystemService,
    predictiveIntelligence,
});
// Setup socket handlers
(0, index_2.setupSocketHandlers)(io, {
    aiOrchestrator,
    systemMonitor,
    terminalManager,
    fileSystemService,
    predictiveIntelligence,
});
// Start system monitoring
systemMonitor.start();
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    systemMonitor.stop();
    terminalManager.cleanup();
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    systemMonitor.stop();
    terminalManager.cleanup();
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
server.listen(PORT, () => {
    console.log(`🚀 NEXUS AI Server running on port ${PORT}`);
    console.log(`🌐 Frontend: http://localhost:3000`);
    console.log(`🔧 API: http://localhost:${PORT}/api`);
    console.log(`🤖 AI Providers initialized: ${aiOrchestrator.getAvailableProviders().length}`);
});
